#!/usr/bin/env python3
"""
Feature Analysis Visualization for Query Execution Time Prediction
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def create_feature_visualizations():
    """Create comprehensive visualizations of feature analysis"""
    
    # Load data
    train_df = pd.read_csv("Dataset/Dataset/train/train.csv")
    results_df = pd.read_csv("feature_analysis_results.csv")
    
    # Set up the plotting style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Create figure with subplots
    fig = plt.figure(figsize=(20, 24))
    
    # 1. Target Variable Distribution
    ax1 = plt.subplot(4, 3, 1)
    target = train_df['QueryTime']
    
    # Original scale
    plt.hist(target, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    plt.xlabel('Query Time (ms)')
    plt.ylabel('Frequency')
    plt.title('Target Variable Distribution\n(Original Scale)')
    plt.yscale('log')
    
    # Add statistics
    plt.axvline(target.mean(), color='red', linestyle='--', label=f'Mean: {target.mean():,.0f}')
    plt.axvline(target.median(), color='orange', linestyle='--', label=f'Median: {target.median():,.0f}')
    plt.legend()
    
    # 2. Target Variable Distribution (Log Scale)
    ax2 = plt.subplot(4, 3, 2)
    log_target = np.log1p(target)
    plt.hist(log_target, bins=50, alpha=0.7, color='lightgreen', edgecolor='black')
    plt.xlabel('Log(Query Time + 1)')
    plt.ylabel('Frequency')
    plt.title('Target Variable Distribution\n(Log Scale)')
    
    # Add statistics
    plt.axvline(log_target.mean(), color='red', linestyle='--', label=f'Mean: {log_target.mean():.2f}')
    plt.axvline(log_target.median(), color='orange', linestyle='--', label=f'Median: {log_target.median():.2f}')
    plt.legend()
    
    # 3. Feature Importance Comparison
    ax3 = plt.subplot(4, 3, 3)
    top_features = results_df.head(15)
    
    x_pos = np.arange(len(top_features))
    plt.barh(x_pos, top_features['abs_pearson'], alpha=0.7, label='Correlation')
    plt.barh(x_pos, top_features['mutual_info']/10, alpha=0.7, label='MI/10')
    plt.barh(x_pos, top_features['rf_importance']*10, alpha=0.7, label='RF*10')
    
    plt.yticks(x_pos, [f[:25] + '...' if len(f) > 25 else f for f in top_features['feature']])
    plt.xlabel('Importance Score')
    plt.title('Feature Importance Comparison\n(Top 15 Features)')
    plt.legend()
    plt.gca().invert_yaxis()
    
    # 4. Correlation Heatmap (Top Features)
    ax4 = plt.subplot(4, 3, 4)
    top_20_features = results_df.head(20)['feature'].tolist()
    corr_matrix = train_df[top_20_features + ['QueryTime']].corr()
    
    mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
    sns.heatmap(corr_matrix, mask=mask, annot=False, cmap='coolwarm', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": .8})
    plt.title('Correlation Matrix\n(Top 20 Features + Target)')
    plt.xticks(rotation=45, ha='right')
    plt.yticks(rotation=0)
    
    # 5. Operation Type Usage
    ax5 = plt.subplot(4, 3, 5)
    
    # Calculate operation usage
    operation_usage = {}
    features = [col for col in train_df.columns if col != 'QueryTime']
    
    operation_types = ['ClusteredIndexScan', 'ClusteredIndexSeek', 'ComputeScalar', 
                      'Concatenation', 'Filter', 'HashMatch', 'NestedLoops', 'Top',
                      'Segment', 'Sort', 'RowCountSpool', 'StreamAggregate', 
                      'TableSpool', 'MergeJoin', 'Assert', 'SequenceProject', 'IndexSpool']
    
    for op_type in operation_types:
        op_count_feature = f"{op_type}OpCount"
        if op_count_feature in train_df.columns:
            usage_pct = (train_df[op_count_feature] > 0).mean() * 100
            operation_usage[op_type] = usage_pct
    
    # Sort by usage
    sorted_ops = sorted(operation_usage.items(), key=lambda x: x[1], reverse=True)
    op_names, usage_pcts = zip(*sorted_ops)
    
    plt.barh(range(len(op_names)), usage_pcts, color='lightcoral')
    plt.yticks(range(len(op_names)), op_names)
    plt.xlabel('Usage Percentage (%)')
    plt.title('SQL Operation Usage Frequency')
    plt.gca().invert_yaxis()
    
    # 6. Feature Ranking Comparison
    ax6 = plt.subplot(4, 3, 6)
    top_10 = results_df.head(10)
    
    x = np.arange(len(top_10))
    width = 0.2
    
    plt.bar(x - 1.5*width, top_10['corr_rank'], width, label='Correlation Rank', alpha=0.8)
    plt.bar(x - 0.5*width, top_10['mi_rank'], width, label='MI Rank', alpha=0.8)
    plt.bar(x + 0.5*width, top_10['rf_rank'], width, label='RF Rank', alpha=0.8)
    plt.bar(x + 1.5*width, top_10['lasso_rank'], width, label='Lasso Rank', alpha=0.8)
    
    plt.xlabel('Features')
    plt.ylabel('Rank (lower is better)')
    plt.title('Feature Ranking Comparison\n(Top 10 Features)')
    plt.xticks(x, [f[:15] + '...' if len(f) > 15 else f for f in top_10['feature']], rotation=45, ha='right')
    plt.legend()
    plt.yscale('log')
    
    # 7. Query Time vs Top Features Scatter
    ax7 = plt.subplot(4, 3, 7)
    
    # Select top 3 features for scatter plot
    top_3_features = results_df.head(3)['feature'].tolist()
    
    for i, feature in enumerate(top_3_features):
        plt.scatter(train_df[feature], target, alpha=0.5, s=20, label=feature[:20])
    
    plt.xlabel('Feature Value')
    plt.ylabel('Query Time (ms)')
    plt.title('Query Time vs Top 3 Features')
    plt.yscale('log')
    plt.legend()
    
    # 8. Feature Value Distributions
    ax8 = plt.subplot(4, 3, 8)
    
    # Box plot of top 5 features (normalized)
    top_5_features = results_df.head(5)['feature'].tolist()
    data_to_plot = []
    labels = []
    
    for feature in top_5_features:
        # Normalize to 0-1 scale for comparison
        feature_data = train_df[feature]
        if feature_data.max() > 0:
            normalized_data = (feature_data - feature_data.min()) / (feature_data.max() - feature_data.min())
            data_to_plot.append(normalized_data)
            labels.append(feature[:15] + '...' if len(feature) > 15 else feature)
    
    plt.boxplot(data_to_plot, labels=labels)
    plt.ylabel('Normalized Value (0-1)')
    plt.title('Feature Value Distributions\n(Top 5 Features, Normalized)')
    plt.xticks(rotation=45, ha='right')
    
    # 9. Lasso Coefficient Magnitude
    ax9 = plt.subplot(4, 3, 9)
    
    # Get non-zero Lasso coefficients
    lasso_features = results_df[results_df['abs_lasso_coef'] > 0].head(15)
    
    colors = ['red' if coef < 0 else 'blue' for coef in lasso_features['abs_lasso_coef']]
    plt.barh(range(len(lasso_features)), lasso_features['abs_lasso_coef'], color=colors, alpha=0.7)
    plt.yticks(range(len(lasso_features)), [f[:20] + '...' if len(f) > 20 else f for f in lasso_features['feature']])
    plt.xlabel('Absolute Lasso Coefficient')
    plt.title('Lasso Feature Selection\n(Top 15 Non-zero Coefficients)')
    plt.xscale('log')
    plt.gca().invert_yaxis()
    
    # 10. Average Ranking
    ax10 = plt.subplot(4, 3, 10)
    
    top_15_avg = results_df.head(15)
    plt.barh(range(len(top_15_avg)), top_15_avg['avg_rank'], color='purple', alpha=0.7)
    plt.yticks(range(len(top_15_avg)), [f[:20] + '...' if len(f) > 20 else f for f in top_15_avg['feature']])
    plt.xlabel('Average Rank')
    plt.title('Comprehensive Feature Ranking\n(Lower is Better)')
    plt.gca().invert_yaxis()
    
    # 11. Operation Type Correlation
    ax11 = plt.subplot(4, 3, 11)
    
    # Calculate average correlation by operation type
    op_correlations = {}
    for op_type in operation_types:
        op_features = [f for f in features if op_type in f]
        if op_features:
            correlations = []
            for feature in op_features:
                corr = train_df[feature].corr(target)
                if not np.isnan(corr):
                    correlations.append(abs(corr))
            if correlations:
                op_correlations[op_type] = np.mean(correlations)
    
    # Sort by correlation
    sorted_corr = sorted(op_correlations.items(), key=lambda x: x[1], reverse=True)
    op_names_corr, corr_values = zip(*sorted_corr)
    
    plt.barh(range(len(op_names_corr)), corr_values, color='orange', alpha=0.7)
    plt.yticks(range(len(op_names_corr)), op_names_corr)
    plt.xlabel('Average Absolute Correlation')
    plt.title('Operation Type Importance\n(by Average Correlation)')
    plt.gca().invert_yaxis()
    
    # 12. Feature Selection Summary
    ax12 = plt.subplot(4, 3, 12)
    
    # Summary statistics
    methods = ['Correlation', 'Mutual Info', 'Random Forest', 'Lasso']
    top_10_counts = [
        len(results_df[results_df['corr_rank'] <= 10]),
        len(results_df[results_df['mi_rank'] <= 10]),
        len(results_df[results_df['rf_rank'] <= 10]),
        len(results_df[results_df['lasso_rank'] <= 10])
    ]
    
    colors = ['skyblue', 'lightgreen', 'lightcoral', 'plum']
    plt.bar(methods, top_10_counts, color=colors, alpha=0.8)
    plt.ylabel('Number of Features in Top 10')
    plt.title('Feature Selection Method Comparison\n(Features in Top 10)')
    plt.xticks(rotation=45)
    
    # Add value labels on bars
    for i, v in enumerate(top_10_counts):
        plt.text(i, v + 0.1, str(v), ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('comprehensive_feature_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Comprehensive feature analysis visualization saved as 'comprehensive_feature_analysis.png'")

def create_top_features_detailed_analysis():
    """Create detailed analysis of top features"""
    
    train_df = pd.read_csv("Dataset/Dataset/train/train.csv")
    results_df = pd.read_csv("feature_analysis_results.csv")
    target = train_df['QueryTime']
    
    # Get top 10 features
    top_10_features = results_df.head(10)['feature'].tolist()
    
    fig, axes = plt.subplots(2, 5, figsize=(25, 10))
    axes = axes.flatten()
    
    for i, feature in enumerate(top_10_features):
        ax = axes[i]
        
        # Scatter plot with trend line
        feature_data = train_df[feature]
        
        # Remove zeros for better visualization
        non_zero_mask = feature_data > 0
        if non_zero_mask.sum() > 10:  # If we have enough non-zero values
            x_data = feature_data[non_zero_mask]
            y_data = target[non_zero_mask]
        else:
            x_data = feature_data
            y_data = target
        
        ax.scatter(x_data, y_data, alpha=0.6, s=20)
        
        # Add trend line if correlation is significant
        if len(x_data) > 10:
            z = np.polyfit(x_data, y_data, 1)
            p = np.poly1d(z)
            ax.plot(x_data, p(x_data), "r--", alpha=0.8)
        
        # Calculate correlation
        corr = results_df[results_df['feature'] == feature]['abs_pearson'].iloc[0]
        
        ax.set_xlabel(feature[:30] + '...' if len(feature) > 30 else feature)
        ax.set_ylabel('Query Time (ms)')
        ax.set_title(f'Rank #{i+1}\nCorr: {corr:.3f}')
        ax.set_yscale('log')
        
        if x_data.max() > 0:
            ax.set_xscale('log')
    
    plt.tight_layout()
    plt.savefig('top_10_features_detailed_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("Top 10 features detailed analysis saved as 'top_10_features_detailed_analysis.png'")

if __name__ == "__main__":
    print("Creating comprehensive feature analysis visualizations...")
    create_feature_visualizations()
    
    print("\nCreating detailed analysis of top features...")
    create_top_features_detailed_analysis()
    
    print("\nVisualization complete!")
