#!/usr/bin/env python3
"""
Debug training metrics calculation in the neural network
"""

import numpy as np
import torch
import torch.nn as nn
from sklearn.metrics import r2_score, mean_squared_error
from sklearn.preprocessing import StandardScaler, RobustScaler
import pandas as pd
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def debug_training_metrics():
    """Debug the training metrics calculation issue"""
    print("🔍 DEBUGGING TRAINING METRICS CALCULATION")
    print("=" * 60)
    
    # Load actual data to test with
    try:
        train_df = pd.read_csv('Dataset/Dataset/train/train2.csv')
        test_df = pd.read_csv('Dataset/Dataset/test/test2.csv')
        print(f"✅ Loaded data: Train={len(train_df)}, Test={len(test_df)}")
    except Exception as e:
        print(f"❌ Could not load data files: {e}")
        return False
    
    # Extract features and targets
    feature_cols = [
        'EstimatedTotalSubtreeCostHashMatch',
        'EstimateRowsHashMatch', 
        'total_num_joins',
        'ClusteredIndexScanOpCount',
        'ClusteredIndexSeekOpCount',
        'SortOpCount',
        'total_estimated_cpu_cost',
        'total_estimated_io_cost',
        'EstimateRowsSort',
        'HashMatchOpCount'
    ]
    
    # Check available features
    available_features = [f for f in feature_cols if f in train_df.columns]
    print(f"Available features: {len(available_features)}/{len(feature_cols)}")
    
    if len(available_features) == 0:
        print("❌ No features available")
        return False
    
    X_train = train_df[available_features].fillna(0).values
    y_train_orig = train_df['QueryTime'].values
    
    X_test = test_df[available_features].fillna(0).values  
    y_test_orig = test_df['QueryTime'].values
    
    print(f"Training data: X={X_train.shape}, y={y_train_orig.shape}")
    print(f"Test data: X={X_test.shape}, y={y_test_orig.shape}")
    print(f"Target range: [{y_train_orig.min():.1f}, {y_train_orig.max():.1f}]")
    
    # Preprocessing
    feature_scaler = RobustScaler()
    target_scaler = StandardScaler()
    
    X_train_scaled = feature_scaler.fit_transform(X_train)
    X_test_scaled = feature_scaler.transform(X_test)
    
    # Target preprocessing (same as in main code)
    y_train_log = np.log1p(y_train_orig)
    y_train_scaled = target_scaler.fit_transform(y_train_log.reshape(-1, 1)).flatten()
    
    y_test_log = np.log1p(y_test_orig)
    y_test_scaled = target_scaler.transform(y_test_log.reshape(-1, 1)).flatten()
    
    print(f"Scaled target range: [{y_train_scaled.min():.3f}, {y_train_scaled.max():.3f}]")
    
    # Create simple model
    class SimpleModel(nn.Module):
        def __init__(self, input_size):
            super().__init__()
            self.layers = nn.Sequential(
                nn.Linear(input_size, 64),
                nn.Tanh(),
                nn.Dropout(0.1),
                nn.Linear(64, 32),
                nn.Tanh(),
                nn.Dropout(0.1),
                nn.Linear(32, 1)
            )
        
        def forward(self, x):
            return self.layers(x)
    
    model = SimpleModel(X_train_scaled.shape[1])
    criterion = nn.MSELoss()
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train_scaled)
    y_train_tensor = torch.FloatTensor(y_train_scaled)
    
    # Test 1: Check initial predictions
    print("\n🔍 TEST 1: INITIAL PREDICTIONS")
    print("-" * 40)
    
    model.eval()
    with torch.no_grad():
        initial_pred_scaled = model(X_train_tensor).squeeze().numpy()
    
    print(f"Initial predictions (scaled): [{initial_pred_scaled.min():.3f}, {initial_pred_scaled.max():.3f}]")
    
    # Convert to original scale
    initial_pred_log = target_scaler.inverse_transform(initial_pred_scaled.reshape(-1, 1)).flatten()
    initial_pred_orig = np.expm1(initial_pred_log)
    initial_pred_orig = np.maximum(initial_pred_orig, 0)
    
    print(f"Initial predictions (original): [{initial_pred_orig.min():.1f}, {initial_pred_orig.max():.1f}]")
    
    # Calculate R² on original scale
    initial_r2 = r2_score(y_train_orig, initial_pred_orig)
    print(f"Initial R² (original scale): {initial_r2:.6f}")
    
    # Calculate R² on scaled scale
    initial_r2_scaled = r2_score(y_train_scaled, initial_pred_scaled)
    print(f"Initial R² (scaled): {initial_r2_scaled:.6f}")
    
    # Test 2: Check loss calculation
    print("\n🔍 TEST 2: LOSS CALCULATION")
    print("-" * 40)
    
    model.train()
    pred_for_loss = model(X_train_tensor).squeeze()
    loss = criterion(pred_for_loss, y_train_tensor)
    print(f"Training loss (MSE on scaled targets): {loss.item():.6f}")
    
    # Manual MSE calculation
    manual_mse = np.mean((initial_pred_scaled - y_train_scaled) ** 2)
    print(f"Manual MSE (scaled): {manual_mse:.6f}")
    
    # Test 3: Check if train/eval mode affects metrics
    print("\n🔍 TEST 3: TRAIN VS EVAL MODE METRICS")
    print("-" * 40)
    
    # Predictions in train mode
    model.train()
    with torch.no_grad():
        pred_train_mode = model(X_train_tensor).squeeze().numpy()
    
    # Predictions in eval mode  
    model.eval()
    with torch.no_grad():
        pred_eval_mode = model(X_train_tensor).squeeze().numpy()
    
    diff = np.abs(pred_train_mode - pred_eval_mode).max()
    print(f"Max difference train vs eval: {diff:.6f}")
    
    # R² in both modes
    r2_train_mode = r2_score(y_train_scaled, pred_train_mode)
    r2_eval_mode = r2_score(y_train_scaled, pred_eval_mode)
    
    print(f"R² in train mode: {r2_train_mode:.6f}")
    print(f"R² in eval mode: {r2_eval_mode:.6f}")
    
    # Test 4: Check data consistency
    print("\n🔍 TEST 4: DATA CONSISTENCY CHECK")
    print("-" * 40)
    
    # Check if targets are consistent
    print(f"Original targets sample: {y_train_orig[:5]}")
    print(f"Scaled targets sample: {y_train_scaled[:5]}")
    
    # Reverse transform scaled targets
    recovered_log = target_scaler.inverse_transform(y_train_scaled.reshape(-1, 1)).flatten()
    recovered_orig = np.expm1(recovered_log)
    
    print(f"Recovered targets sample: {recovered_orig[:5]}")
    
    max_recovery_error = np.abs(y_train_orig - recovered_orig).max()
    print(f"Max recovery error: {max_recovery_error:.10f}")
    
    if max_recovery_error < 1e-10:
        print("✅ Target preprocessing is consistent")
    else:
        print("❌ Target preprocessing has errors")
    
    return True

def main():
    """Run debugging"""
    print("🐛 TRAINING METRICS DEBUG SESSION")
    print("=" * 80)
    
    try:
        debug_training_metrics()
        print("\n🎉 DEBUG SESSION COMPLETED")
    except Exception as e:
        print(f"❌ DEBUG FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
