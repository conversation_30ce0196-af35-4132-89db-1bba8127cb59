import numpy as np
from sklearn.metrics import r2_score, mean_squared_error

# Create simple test data to verify our metric calculations
np.random.seed(42)
true_values = np.array([100, 200, 300, 400, 500])
predicted_values = np.array([110, 190, 310, 390, 510])

print("=== METRIC VERIFICATION ===")
print(f"True values: {true_values}")
print(f"Predicted values: {predicted_values}")

# Calculate R² manually
ss_res = np.sum((true_values - predicted_values) ** 2)
ss_tot = np.sum((true_values - np.mean(true_values)) ** 2)
r2_manual = 1 - (ss_res / ss_tot)

# Calculate R² using sklearn
r2_sklearn = r2_score(true_values, predicted_values)

# Calculate RMSE manually
rmse_manual = np.sqrt(np.mean((true_values - predicted_values) ** 2))

# Calculate RMSE using sklearn
rmse_sklearn = np.sqrt(mean_squared_error(true_values, predicted_values))

print(f"\nR² manual calculation: {r2_manual:.6f}")
print(f"R² sklearn calculation: {r2_sklearn:.6f}")
print(f"R² match: {np.isclose(r2_manual, r2_sklearn)}")

print(f"\nRMSE manual calculation: {rmse_manual:.6f}")
print(f"RMSE sklearn calculation: {rmse_sklearn:.6f}")
print(f"RMSE match: {np.isclose(rmse_manual, rmse_sklearn)}")

# Test with perfect predictions
perfect_pred = true_values.copy()
r2_perfect = r2_score(true_values, perfect_pred)
print(f"\nPerfect prediction R²: {r2_perfect:.6f} (should be 1.0)")

# Test with worst predictions (mean prediction)
mean_pred = np.full_like(true_values, np.mean(true_values))
r2_worst = r2_score(true_values, mean_pred)
print(f"Mean prediction R²: {r2_worst:.6f} (should be 0.0)")

# Test with very bad predictions
bad_pred = np.array([1000, 2000, 3000, 4000, 5000])
r2_bad = r2_score(true_values, bad_pred)
print(f"Bad prediction R²: {r2_bad:.6f} (should be negative)")
