import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Tuple, Dict
import warnings
warnings.filterwarnings('ignore')

class QueryExecutionTimePredictor(nn.Module):
    """
    Neural Network for Database Query Execution Time Prediction
    Architecture: Multi-layer perceptron with tanh activation and batch normalization
    """
    def __init__(self, input_size: int, hidden_sizes: list = [256, 128, 64, 32]):
        super(QueryExecutionTimePredictor, self).__init__()
        
        layers = []
        prev_size = input_size
        
        # Build hidden layers with tanh activation
        for hidden_size in hidden_sizes:
            layers.extend([
                nn.Linear(prev_size, hidden_size),
                nn.BatchNorm1d(hidden_size),
                nn.Tanh()  # Changed from ReLU to Tanh
            ])
            prev_size = hidden_size
        
        # Output layer
        layers.append(nn.Linear(prev_size, 1))
        
        self.network = nn.Sequential(*layers)
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.constant_(module.bias, 0)
    
    def forward(self, x):
        return self.network(x)

class QueryTimeDataset:
    """Data loading and preprocessing for query execution time prediction"""
    
    def __init__(self, train_path: str, test_path: str):
        self.train_path = train_path
        self.test_path = test_path
        self.scaler = RobustScaler()  # Better for outliers than StandardScaler
        self.target_scaler = RobustScaler()
        
    def load_and_preprocess(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Load and preprocess training and test data"""
        
        # Load training data
        print("Loading training data...")
        train_df = pd.read_csv(self.train_path)
        
        # Load test data
        print("Loading test data...")
        test_df = pd.read_csv(self.test_path)
        
        # Separate features and target
        feature_columns = [col for col in train_df.columns if col != 'QueryTime']
        
        X_train_full = train_df[feature_columns].values
        y_train_full = train_df['QueryTime'].values.reshape(-1, 1)
        
        # Handle test data (might not have QueryTime column)
        if 'QueryTime' in test_df.columns:
            X_test = test_df[feature_columns].values
            y_test = test_df['QueryTime'].values.reshape(-1, 1)
        else:
            X_test = test_df[feature_columns].values
            y_test = None
        
        # Handle missing values and outliers
        X_train_full = self._handle_missing_values(X_train_full)
        X_test = self._handle_missing_values(X_test)
        
        # Split training data into train/validation
        X_train, X_val, y_train, y_val = train_test_split(
            X_train_full, y_train_full, test_size=0.2, random_state=42
        )
        
        # Fit scalers on training data only
        X_train_scaled = self.scaler.fit_transform(X_train)
        y_train_scaled = self.target_scaler.fit_transform(y_train)
        
        # Transform validation and test data
        X_val_scaled = self.scaler.transform(X_val)
        y_val_scaled = self.target_scaler.transform(y_val)
        X_test_scaled = self.scaler.transform(X_test)
        
        print(f"Training set: {X_train_scaled.shape}")
        print(f"Validation set: {X_val_scaled.shape}")
        print(f"Test set: {X_test_scaled.shape}")
        
        return (X_train_scaled, y_train_scaled, X_val_scaled, y_val_scaled, 
                X_test_scaled, y_test, y_train, y_val)
    
    def _handle_missing_values(self, X: np.ndarray) -> np.ndarray:
        """Handle missing values and infinite values"""
        # Replace inf with nan
        X = np.where(np.isinf(X), np.nan, X)
        
        # Fill missing values with median
        from sklearn.impute import SimpleImputer
        imputer = SimpleImputer(strategy='median')
        X = imputer.fit_transform(X)
        
        return X

class NeuralNetworkTrainer:
    """Training and evaluation pipeline for neural network"""
    
    def __init__(self, model: nn.Module, device: str = 'cuda' if torch.cuda.is_available() else 'cpu'):
        self.model = model.to(device)
        self.device = device
        self.train_losses = []
        self.val_losses = []
        
    def train(self, X_train: np.ndarray, y_train: np.ndarray, 
              X_val: np.ndarray, y_val: np.ndarray,
              epochs: int = 1000, batch_size: int = 64, 
              learning_rate: float = 0.001, patience: int = 20) -> Dict:
        """Train the neural network with early stopping"""
        
        # Convert to tensors
        X_train_tensor = torch.FloatTensor(X_train).to(self.device)
        y_train_tensor = torch.FloatTensor(y_train).to(self.device)
        X_val_tensor = torch.FloatTensor(X_val).to(self.device)
        y_val_tensor = torch.FloatTensor(y_val).to(self.device)
        
        # Create data loaders
        train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
        
        # Optimizer and loss function
        optimizer = optim.AdamW(self.model.parameters(), lr=learning_rate, weight_decay=1e-5)
        scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
        criterion = nn.MSELoss()
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        print("Starting training...")
        for epoch in range(epochs):
            # Training phase
            self.model.train()
            train_loss = 0.0
            
            for batch_X, batch_y in train_loader:
                optimizer.zero_grad()
                outputs = self.model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                
                # Gradient clipping
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                
                optimizer.step()
                train_loss += loss.item()
            
            # Validation phase
            self.model.eval()
            with torch.no_grad():
                val_outputs = self.model(X_val_tensor)
                val_loss = criterion(val_outputs, y_val_tensor).item()
            
            # Learning rate scheduling
            scheduler.step(val_loss)
            
            # Record losses
            avg_train_loss = train_loss / len(train_loader)
            self.train_losses.append(avg_train_loss)
            self.val_losses.append(val_loss)
            
            # Early stopping
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # Save best model
                torch.save(self.model.state_dict(), 'best_model.pth')
            else:
                patience_counter += 1
            
            if epoch % 10 == 0:
                print(f'Epoch {epoch:3d}: Train Loss: {avg_train_loss:.6f}, '
                      f'Val Loss: {val_loss:.6f}, LR: {optimizer.param_groups[0]["lr"]:.2e}')
            
            if patience_counter >= patience and epoch >= 100:  # Ensure minimum 100 epochs
                print(f'Early stopping at epoch {epoch}')
                break
        
        # Load best model
        self.model.load_state_dict(torch.load('best_model.pth'))
        
        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'best_val_loss': best_val_loss
        }
    
    def evaluate(self, X: np.ndarray, y_true: np.ndarray, 
                 target_scaler, phase: str = "Test") -> Dict:
        """Evaluate model performance"""
        self.model.eval()
        
        with torch.no_grad():
            X_tensor = torch.FloatTensor(X).to(self.device)
            y_pred_scaled = self.model(X_tensor).cpu().numpy()
        
        # Inverse transform predictions and true values
        y_pred = target_scaler.inverse_transform(y_pred_scaled)
        y_true_original = target_scaler.inverse_transform(y_true.reshape(-1, 1))
        
        # Calculate metrics
        mse = mean_squared_error(y_true_original, y_pred)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_true_original, y_pred)
        r2 = r2_score(y_true_original, y_pred)
        
        # Calculate MAPE (avoiding division by zero)
        mask = y_true_original.flatten() != 0
        mape = np.mean(np.abs((y_true_original.flatten()[mask] - y_pred.flatten()[mask]) / 
                             y_true_original.flatten()[mask])) * 100
        
        metrics = {
            'MSE': mse,
            'RMSE': rmse,
            'MAE': mae,
            'R2': r2,
            'MAPE': mape
        }
        
        print(f"\n{phase} Results:")
        print(f"RMSE: {rmse:.2f}")
        print(f"MAE: {mae:.2f}")
        print(f"R²: {r2:.4f}")
        print(f"MAPE: {mape:.2f}%")
        
        return metrics, y_pred.flatten(), y_true_original.flatten()

def plot_results(trainer: NeuralNetworkTrainer, y_true: np.ndarray, 
                y_pred: np.ndarray, metrics: Dict):
    """Plot training curves and prediction results"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Training curves
    axes[0, 0].plot(trainer.train_losses, label='Training Loss', alpha=0.7)
    axes[0, 0].plot(trainer.val_losses, label='Validation Loss', alpha=0.7)
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].set_title('Training and Validation Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # Predictions vs Actual
    axes[0, 1].scatter(y_true, y_pred, alpha=0.6, s=20)
    axes[0, 1].plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 
                    'r--', lw=2, label='Perfect Prediction')
    axes[0, 1].set_xlabel('Actual Query Time')
    axes[0, 1].set_ylabel('Predicted Query Time')
    axes[0, 1].set_title(f'Predictions vs Actual (R² = {metrics["R2"]:.4f})')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # Residuals
    residuals = y_true - y_pred
    axes[1, 0].scatter(y_pred, residuals, alpha=0.6, s=20)
    axes[1, 0].axhline(y=0, color='r', linestyle='--')
    axes[1, 0].set_xlabel('Predicted Query Time')
    axes[1, 0].set_ylabel('Residuals')
    axes[1, 0].set_title('Residual Plot')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Error distribution
    axes[1, 1].hist(residuals, bins=50, alpha=0.7, edgecolor='black')
    axes[1, 1].set_xlabel('Residuals')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].set_title('Residual Distribution')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('neural_network_results.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """Main execution pipeline"""
    
    # File paths
    train_path = "Dataset/Dataset/train/train.csv"
    test_path = "Dataset/Dataset/test/test.csv"
    
    # Load and preprocess data
    dataset = QueryTimeDataset(train_path, test_path)
    (X_train, y_train_scaled, X_val, y_val_scaled, 
     X_test, y_test, y_train_orig, y_val_orig) = dataset.load_and_preprocess()
    
    # Create model with tanh activation and no dropout
    input_size = X_train.shape[1]
    model = QueryExecutionTimePredictor(
        input_size=input_size,
        hidden_sizes=[256, 128, 64, 32]
    )
    
    print(f"Model architecture: {input_size} -> 256 -> 128 -> 64 -> 32 -> 1")
    print(f"Activation function: Tanh")
    print(f"Dropout: Disabled")
    print(f"Total parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Train model for minimum 100 epochs
    trainer = NeuralNetworkTrainer(model)
    training_history = trainer.train(
        X_train, y_train_scaled, X_val, y_val_scaled,
        epochs=200, batch_size=64, learning_rate=0.001, patience=30
    )
    
    # Evaluate on validation set
    val_metrics, val_pred, val_true = trainer.evaluate(
        X_val, y_val_orig, dataset.target_scaler, "Validation"
    )
    
    # Evaluate on test set (if labels available)
    if y_test is not None:
        test_metrics, test_pred, test_true = trainer.evaluate(
            X_test, y_test, dataset.target_scaler, "Test"
        )
        
        # Plot results
        plot_results(trainer, test_true, test_pred, test_metrics)
    else:
        # Generate predictions for test set
        model.eval()
        with torch.no_grad():
            X_test_tensor = torch.FloatTensor(X_test).to(trainer.device)
            test_pred_scaled = model(X_test_tensor).cpu().numpy()
            test_pred = dataset.target_scaler.inverse_transform(test_pred_scaled)
        
        # Save predictions
        pd.DataFrame({'QueryTime_Predicted': test_pred.flatten()}).to_csv(
            'test_predictions.csv', index=False
        )
        print("Test predictions saved to 'test_predictions.csv'")
        
        # Plot validation results
        plot_results(trainer, val_true, val_pred, val_metrics)
    
    # Save model
    torch.save({
        'model_state_dict': model.state_dict(),
        'scaler': dataset.scaler,
        'target_scaler': dataset.target_scaler,
        'input_size': input_size
    }, 'query_time_predictor.pth')
    
    print("\nModel saved as 'query_time_predictor.pth'")
    
    return model, trainer, dataset

if __name__ == "__main__":
    model, trainer, dataset = main()
