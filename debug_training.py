import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import matplotlib.pyplot as plt

def diagnose_training_issues():
    """Diagnose common training problems"""
    
    # Load data to check distributions
    train_df = pd.read_csv("Dataset/Dataset/train/train.csv")
    
    print("=== DATA ANALYSIS ===")
    print(f"Training samples: {len(train_df)}")
    print(f"Features: {len(train_df.columns) - 1}")
    
    # Check target variable distribution
    query_times = train_df['QueryTime']
    print(f"\nQuery Time Statistics:")
    print(f"Min: {query_times.min():.2f}")
    print(f"Max: {query_times.max():.2f}")
    print(f"Mean: {query_times.mean():.2f}")
    print(f"Std: {query_times.std():.2f}")
    print(f"Range: {query_times.max() - query_times.min():.2f}")
    
    # Check for extreme values
    q99 = query_times.quantile(0.99)
    q01 = query_times.quantile(0.01)
    print(f"99th percentile: {q99:.2f}")
    print(f"1st percentile: {q01:.2f}")
    
    # Check feature distributions
    features = train_df.drop('QueryTime', axis=1)
    print(f"\nFeature Statistics:")
    print(f"Features with zeros: {(features == 0).sum().sum()}")
    print(f"Features with NaN: {features.isna().sum().sum()}")
    print(f"Features with inf: {np.isinf(features.values).sum()}")
    
    # Plot distributions
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # Query time distribution
    axes[0,0].hist(query_times, bins=50, alpha=0.7)
    axes[0,0].set_title('Query Time Distribution')
    axes[0,0].set_xlabel('Query Time')
    
    # Log query time distribution
    log_times = np.log1p(query_times)
    axes[0,1].hist(log_times, bins=50, alpha=0.7)
    axes[0,1].set_title('Log(Query Time + 1) Distribution')
    axes[0,1].set_xlabel('Log Query Time')
    
    # Feature value ranges
    feature_ranges = features.max() - features.min()
    axes[1,0].hist(feature_ranges, bins=30, alpha=0.7)
    axes[1,0].set_title('Feature Value Ranges')
    axes[1,0].set_xlabel('Range')
    
    # Feature means
    feature_means = features.mean()
    axes[1,1].hist(feature_means, bins=30, alpha=0.7)
    axes[1,1].set_title('Feature Means')
    axes[1,1].set_xlabel('Mean Value')
    
    plt.tight_layout()
    plt.savefig('data_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return query_times, features

if __name__ == "__main__":
    query_times, features = diagnose_training_issues()