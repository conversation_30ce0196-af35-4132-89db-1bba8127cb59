import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler, RobustScaler, LabelEncoder, MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.feature_selection import mutual_info_regression, SelectKBest
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Tuple, Dict, List, Optional
import warnings
import time
import json
import os
from datetime import datetime
warnings.filterwarnings('ignore')

class AdvancedQueryPredictor(nn.Module):
    """Advanced neural network for query time prediction using MSE loss"""

    def __init__(self, input_size: int, hidden_sizes: List[int] = [128, 64, 32],
                 dropout_rate: float = 0.3):
        super(AdvancedQueryPredictor, self).__init__()

        self.input_size = input_size
        self.hidden_sizes = hidden_sizes

        # Input layer with batch normalization
        self.input_layer = nn.Sequential(
            nn.Linear(input_size, hidden_sizes[0]),
            nn.BatchNorm1d(hidden_sizes[0]),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )

        # Hidden layers with residual connections
        self.hidden_layers = nn.ModuleList()
        for i in range(len(hidden_sizes) - 1):
            layer = nn.Sequential(
                nn.Linear(hidden_sizes[i], hidden_sizes[i + 1]),
                nn.BatchNorm1d(hidden_sizes[i + 1]),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            )
            self.hidden_layers.append(layer)

        # Single output layer for regression
        self.output_layer = nn.Linear(hidden_sizes[-1], 1)

        # Initialize weights properly
        self._initialize_weights()

    def _initialize_weights(self):
        """Xavier/Glorot initialization for better gradient flow"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.zeros_(module.bias)

    def forward(self, x):
        # Input layer
        x = self.input_layer(x)

        # Hidden layers with residual connections
        for i, layer in enumerate(self.hidden_layers):
            residual = x
            x = layer(x)

            # Add residual connection if dimensions match
            if x.shape == residual.shape and i > 0:
                x = x + residual

        return self.output_layer(x)




class AdvancedFeatureExtractor:
    """Enhanced feature extraction and engineering for query execution plans"""

    def __init__(self):
        self.scalers = {
            'numerical': RobustScaler(),
            'categorical': LabelEncoder(),
            'temporal': MinMaxScaler()
        }
        self.feature_selector = None
        self.selected_features = None

    def extract_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Extract advanced features from execution plan data"""

        # Identify feature types
        numerical_features = df.select_dtypes(include=[np.number]).columns.tolist()
        if 'QueryTime' in numerical_features:
            numerical_features.remove('QueryTime')

        # Feature engineering
        engineered_df = df.copy()

        # Create interaction features for important combinations
        if len(numerical_features) >= 2:
            # Cost-cardinality interactions
            cost_features = [f for f in numerical_features if 'cost' in f.lower()]
            card_features = [f for f in numerical_features if 'card' in f.lower() or 'row' in f.lower()]

            for cost_f in cost_features[:3]:  # Limit to avoid explosion
                for card_f in card_features[:3]:
                    if cost_f in df.columns and card_f in df.columns:
                        engineered_df[f'{cost_f}_x_{card_f}'] = df[cost_f] * df[card_f]

        # Create polynomial features for key metrics
        key_features = numerical_features[:5]  # Top 5 most important
        for feature in key_features:
            if feature in df.columns:
                engineered_df[f'{feature}_squared'] = df[feature] ** 2
                engineered_df[f'{feature}_log'] = np.log1p(np.abs(df[feature]))

        return engineered_df

    def select_features(self, X: np.ndarray, y: np.ndarray, k: int = 50) -> np.ndarray:
        """Select top k features using mutual information"""

        if self.feature_selector is None:
            self.feature_selector = SelectKBest(score_func=mutual_info_regression, k=k)
            X_selected = self.feature_selector.fit_transform(X, y)
            self.selected_features = self.feature_selector.get_support()
        else:
            X_selected = self.feature_selector.transform(X)

        return X_selected


class PlatformAdaptiveModel(nn.Module):
    """Platform-adaptive neural network for cross-platform generalization"""

    def __init__(self, input_size: int, platform_types: List[str] = ['linux_oracle', 'windows_sqlserver']):
        super(PlatformAdaptiveModel, self).__init__()

        self.platform_types = platform_types

        # Platform-specific feature adaptation layers
        self.platform_adapters = nn.ModuleDict({
            platform: nn.Sequential(
                nn.Linear(input_size, input_size),
                nn.BatchNorm1d(input_size),
                nn.ReLU(),
                nn.Dropout(0.1)
            ) for platform in platform_types
        })

        # Shared base model
        self.base_model = AdvancedQueryPredictor(input_size, use_uncertainty=True)

    def forward(self, x, platform: str = 'linux_oracle'):
        """Forward pass with platform adaptation"""
        if platform in self.platform_adapters:
            adapted_x = self.platform_adapters[platform](x)
        else:
            adapted_x = x

        return self.base_model(adapted_x)


class ImprovedDataPreprocessor:
    """Enhanced preprocessing with advanced feature engineering and cross-validation"""

    def __init__(self, train_path: str, test_path: str, use_feature_engineering: bool = True):
        self.train_path = train_path
        self.test_path = test_path
        self.use_feature_engineering = use_feature_engineering
        self.feature_scaler = RobustScaler()
        self.target_scaler = RobustScaler()
        self.feature_extractor = AdvancedFeatureExtractor()
        self.feature_names = None

    def load_and_preprocess(self):
        print("Loading and preprocessing data with advanced feature engineering...")

        # Load data
        train_df = pd.read_csv(self.train_path)
        test_df = pd.read_csv(self.test_path)

        print(f"Original training data shape: {train_df.shape}")
        print(f"Original test data shape: {test_df.shape}")

        # Apply advanced feature engineering
        if self.use_feature_engineering:
            print("Applying advanced feature engineering...")
            train_df = self.feature_extractor.extract_advanced_features(train_df)
            test_df = self.feature_extractor.extract_advanced_features(test_df)
            print(f"After feature engineering - Training: {train_df.shape}, Test: {test_df.shape}")

        # Separate features and target
        feature_columns = [col for col in train_df.columns if col != 'QueryTime']
        self.feature_names = feature_columns

        X_train_full = train_df[feature_columns].values
        y_train_full = train_df['QueryTime'].values

        print(f"Original target range: {y_train_full.min():.0f} to {y_train_full.max():.0f}")
        print(f"Target statistics: mean={y_train_full.mean():.2f}, std={y_train_full.std():.2f}")

        # Handle test data
        if 'QueryTime' in test_df.columns:
            X_test = test_df[feature_columns].values
            y_test_orig = test_df['QueryTime'].values
            y_test_log = np.log1p(y_test_orig).reshape(-1, 1)
        else:
            X_test = test_df[feature_columns].values
            y_test_log = None
            y_test_orig = None

        # Handle missing values and infinite values
        print("Handling missing and infinite values...")
        X_train_full = self._handle_missing_values(X_train_full)
        X_test = self._handle_missing_values(X_test)

        # Feature selection using mutual information
        print("Performing feature selection...")
        y_train_log_flat = np.log1p(y_train_full)
        X_train_selected = self.feature_extractor.select_features(X_train_full, y_train_log_flat)
        X_test_selected = self.feature_extractor.select_features(X_test, y_train_log_flat)

        print(f"Selected {X_train_selected.shape[1]} features out of {X_train_full.shape[1]}")

        # CRITICAL: Check and fix feature scaling issues
        print("Analyzing and fixing feature distributions...")
        X_train_selected, X_test_selected = self._fix_scaling_issues(
            X_train_selected, X_test_selected, list(range(X_train_selected.shape[1]))
        )

        # Log transform target with improved handling
        y_train_log = self._transform_target(y_train_full)
        print(f"Log-transformed target range: {y_train_log.min():.3f} to {y_train_log.max():.3f}")

        # Stratified split based on target quantiles for better validation
        target_quantiles = pd.qcut(y_train_log.flatten(), q=5, labels=False)
        X_train, X_val, y_train_log, y_val_log = train_test_split(
            X_train_selected, y_train_log, test_size=0.2, random_state=42,
            stratify=target_quantiles
        )

        # Get original values for evaluation
        y_train_orig = np.expm1(y_train_log.flatten())
        y_val_orig = np.expm1(y_val_log.flatten())

        # FIX: Fit scalers on TRAINING data only
        print("Fitting scalers on training data only...")
        self.feature_scaler.fit(X_train)
        self.target_scaler.fit(y_train_log)

        # Transform all data with same scaler
        X_train_scaled = self.feature_scaler.transform(X_train)
        y_train_scaled = self.target_scaler.transform(y_train_log)

        X_val_scaled = self.feature_scaler.transform(X_val)
        y_val_scaled = self.target_scaler.transform(y_val_log)
        X_test_scaled = self.feature_scaler.transform(X_test_selected)

        # Validation checks
        self._validate_preprocessing(X_train_scaled, y_train_scaled, X_val_scaled, y_val_scaled)

        return (X_train_scaled, y_train_scaled, X_val_scaled, y_val_scaled,
                X_test_scaled, y_test_log, y_train_orig, y_val_orig, y_test_orig)

    def _handle_missing_values(self, X: np.ndarray) -> np.ndarray:
        """Handle missing and infinite values"""
        # Replace infinite values with NaN
        X = np.where(np.isinf(X), np.nan, X)

        # Fill NaN with median
        for col in range(X.shape[1]):
            col_data = X[:, col]
            if np.isnan(col_data).any():
                median_val = np.nanmedian(col_data)
                X[:, col] = np.where(np.isnan(col_data), median_val, col_data)

        return X

    def _transform_target(self, y: np.ndarray) -> np.ndarray:
        """Improved target transformation"""
        # Handle negative values
        y_positive = np.maximum(y, 1e-6)

        # Apply log1p transformation
        y_log = np.log1p(y_positive).reshape(-1, 1)

        # Remove extreme outliers (beyond 3 standard deviations)
        mean_log = np.mean(y_log)
        std_log = np.std(y_log)
        outlier_mask = np.abs(y_log - mean_log) > 3 * std_log

        if outlier_mask.sum() > 0:
            print(f"Capping {outlier_mask.sum()} extreme outliers in target variable")
            y_log = np.where(outlier_mask,
                           mean_log + 3 * std_log * np.sign(y_log - mean_log),
                           y_log)

        return y_log

    def _validate_preprocessing(self, X_train, y_train, X_val, y_val):
        """Validate preprocessing results"""
        print(f"\nPreprocessing validation:")
        print(f"Training set: X={X_train.shape}, y={y_train.shape}")
        print(f"Validation set: X={X_val.shape}, y={y_val.shape}")
        print(f"Feature range: [{X_train.min():.3f}, {X_train.max():.3f}]")
        print(f"Target range: [{y_train.min():.3f}, {y_train.max():.3f}]")
        print(f"NaN check - Features: {np.isnan(X_train).sum()}, Target: {np.isnan(y_train).sum()}")
        print(f"Inf check - Features: {np.isinf(X_train).sum()}, Target: {np.isinf(y_train).sum()}")

        # Check for data leakage using sample correlation instead of full arrays
        if X_train.shape[0] > 1000 and X_val.shape[0] > 1000:
            # Sample for correlation check to avoid memory issues
            train_sample = X_train[:1000].flatten()
            val_sample = X_val[:1000].flatten()
            feature_corr = np.corrcoef(train_sample, val_sample)[0, 1]
            print(f"Feature correlation between train/val (sampled): {feature_corr:.3f}")

            if feature_corr > 0.99:
                print("WARNING: High correlation between train/val features - possible data leakage!")
        else:
            print("Skipping correlation check due to small dataset size")

        # Additional validation checks
        print(f"Feature variance - Train: {np.var(X_train):.3f}, Val: {np.var(X_val):.3f}")
        print(f"Target variance - Train: {np.var(y_train):.3f}, Val: {np.var(y_val):.3f}")

        return True
    
    def _fix_scaling_issues(self, X_train, X_test, feature_columns):
        """Fix scaling inconsistencies between train and test"""
        
        print("Fixing scaling issues between train and test sets...")
        
        # Convert to DataFrames for easier manipulation
        train_df = pd.DataFrame(X_train, columns=feature_columns)
        test_df = pd.DataFrame(X_test, columns=feature_columns)
        
        scaling_fixes = 0
        
        for col in feature_columns:
            train_col = train_df[col]
            test_col = test_df[col]
            
            # Check for extreme differences
            train_mean = train_col.mean()
            test_mean = test_col.mean()
            train_std = train_col.std()
            test_std = test_col.std()
            
            # Fix extreme outliers in both sets
            combined_col = pd.concat([train_col, test_col])
            q99 = combined_col.quantile(0.99)
            q01 = combined_col.quantile(0.01)
            
            # Clip outliers using combined statistics
            train_df[col] = train_col.clip(q01, q99)
            test_df[col] = test_col.clip(q01, q99)
            
            # Check if scaling is still problematic
            if train_std > 0 and test_std > 0:
                mean_diff = abs(train_mean - test_mean) / (abs(train_mean) + 1e-8)
                if mean_diff > 1.0:  # Very different means
                    scaling_fixes += 1
                    
        print(f"Applied scaling fixes to {scaling_fixes} features")
        
        # Handle infinite and NaN values
        train_df = train_df.replace([np.inf, -np.inf], np.nan)
        test_df = test_df.replace([np.inf, -np.inf], np.nan)
        
        # Fill NaN with median from combined data
        for col in feature_columns:
            combined_median = pd.concat([train_df[col], test_df[col]]).median()
            train_df[col] = train_df[col].fillna(combined_median)
            test_df[col] = test_df[col].fillna(combined_median)
        
        return train_df.values, test_df.values

class ComprehensiveEvaluator:
    """Comprehensive evaluation framework for query time prediction models"""

    def __init__(self):
        self.metrics_history = []
        self.predictions_history = []

    def evaluate_model(self, model, X_val, y_val, y_val_orig, preprocessor, device):
        """Comprehensive model evaluation with multiple metrics"""
        model.eval()

        with torch.no_grad():
            X_val_tensor = torch.FloatTensor(X_val).to(device)

            # Standard model prediction
            val_pred_scaled = model(X_val_tensor).cpu().numpy()
            val_pred_std = None

            # Inverse transform predictions
            val_pred_log = preprocessor.target_scaler.inverse_transform(val_pred_scaled)
            val_pred_orig = np.expm1(val_pred_log.flatten())
            val_pred_orig = np.maximum(val_pred_orig, 0)  # Ensure positive

            # Calculate comprehensive metrics
            metrics = self._calculate_metrics(y_val_orig, val_pred_orig, val_pred_std)

            return metrics, val_pred_orig, val_pred_std

    def _calculate_metrics(self, y_true, y_pred, y_std=None):
        """Calculate comprehensive evaluation metrics including RAE, RRSE, and PR"""

        # Basic regression metrics
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        mae = mean_absolute_error(y_true, y_pred)
        r2 = r2_score(y_true, y_pred)

        # MAPE with proper handling of small values
        mask = y_true > 1e-6
        if mask.sum() > 0:
            mape = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100
        else:
            mape = float('inf')

        # Relative metrics
        relative_rmse = rmse / np.mean(y_true)
        relative_mae = mae / np.mean(y_true)

        # Calculate mean of actual values for baseline comparisons
        y_mean = np.mean(y_true)

        # Relative Absolute Error (RAE)
        # RAE = Σ|y_true - y_pred| / Σ|y_true - y_mean|
        numerator_rae = np.sum(np.abs(y_true - y_pred))
        denominator_rae = np.sum(np.abs(y_true - y_mean))
        rae = numerator_rae / denominator_rae if denominator_rae > 0 else float('inf')

        # Root Relative Squared Error (RRSE)
        # RRSE = sqrt(Σ(y_true - y_pred)² / Σ(y_true - y_mean)²)
        numerator_rrse = np.sum((y_true - y_pred) ** 2)
        denominator_rrse = np.sum((y_true - y_mean) ** 2)
        rrse = np.sqrt(numerator_rrse / denominator_rrse) if denominator_rrse > 0 else float('inf')

        # Predictive Risk (PR) - Expected loss under squared error
        # PR = E[(y_true - y_pred)²] = MSE
        pr = np.mean((y_true - y_pred) ** 2)

        # Additional advanced metrics
        # Normalized RMSE (NRMSE) - RMSE normalized by range
        y_range = y_true.max() - y_true.min()
        nrmse = rmse / y_range if y_range > 0 else float('inf')

        # Mean Absolute Scaled Error (MASE) - for time series-like comparison
        # Using naive forecast (mean) as baseline
        naive_mae = np.mean(np.abs(y_true - y_mean))
        mase = mae / naive_mae if naive_mae > 0 else float('inf')

        # Prediction quality metrics
        prediction_range = y_pred.max() - y_pred.min()
        actual_range = y_true.max() - y_true.min()
        range_ratio = prediction_range / actual_range if actual_range > 0 else 0

        # Correlation metrics
        pearson_corr = stats.pearsonr(y_true, y_pred)[0]
        spearman_corr = stats.spearmanr(y_true, y_pred)[0]

        # Bias metrics
        bias = np.mean(y_pred - y_true)
        bias_percentage = (bias / y_mean) * 100 if y_mean > 0 else 0

        metrics = {
            'rmse': rmse,
            'mae': mae,
            'r2': r2,
            'mape': mape,
            'relative_rmse': relative_rmse,
            'relative_mae': relative_mae,
            'rae': rae,  # Relative Absolute Error
            'rrse': rrse,  # Root Relative Squared Error
            'pr': pr,  # Predictive Risk
            'nrmse': nrmse,  # Normalized RMSE
            'mase': mase,  # Mean Absolute Scaled Error
            'bias': bias,
            'bias_percentage': bias_percentage,
            'range_ratio': range_ratio,
            'pearson_corr': pearson_corr,
            'spearman_corr': spearman_corr,
            'pred_range': prediction_range,
            'actual_range': actual_range,
            'negative_preds': (y_pred < 0).sum(),
            'zero_preds': (y_pred == 0).sum()
        }

        # No uncertainty metrics for MSE model

        return metrics

    def print_metrics(self, metrics, title="Model Evaluation"):
        """Print formatted metrics including new RAE, RRSE, and PR metrics"""
        print(f"\n{title}:")
        print("=" * 60)

        # Basic metrics
        print("Basic Regression Metrics:")
        print("-" * 30)
        print(f"RMSE: {metrics.get('rmse', 0):,.2f}")
        print(f"MAE: {metrics.get('mae', 0):,.2f}")
        print(f"R²: {metrics.get('r2', 0):.4f}")
        print(f"MAPE: {metrics.get('mape', 0):.2f}%")

        # Advanced metrics (RAE, RRSE, PR)
        print("\nAdvanced Evaluation Metrics:")
        print("-" * 30)
        print(f"RAE (Relative Absolute Error): {metrics.get('rae', 0):.4f}")
        print(f"RRSE (Root Relative Squared Error): {metrics.get('rrse', 0):.4f}")
        print(f"PR (Predictive Risk): {metrics.get('pr', 0):,.2f}")
        print(f"NRMSE (Normalized RMSE): {metrics.get('nrmse', 0):.4f}")
        print(f"MASE (Mean Absolute Scaled Error): {metrics.get('mase', 0):.4f}")

        # Relative metrics
        print("\nRelative Performance Metrics:")
        print("-" * 30)
        print(f"Relative RMSE: {metrics.get('relative_rmse', 0):.3f}")
        print(f"Relative MAE: {metrics.get('relative_mae', 0):.3f}")
        print(f"Bias: {metrics.get('bias', 0):,.2f}")
        print(f"Bias Percentage: {metrics.get('bias_percentage', 0):.2f}%")

        # Correlation metrics
        print("\nCorrelation Metrics:")
        print("-" * 30)
        print(f"Pearson Correlation: {metrics.get('pearson_corr', 0):.4f}")
        print(f"Spearman Correlation: {metrics.get('spearman_corr', 0):.4f}")
        print(f"Range Ratio: {metrics.get('range_ratio', 0):.3f}")

        # No uncertainty metrics for MSE model

        # Data quality metrics
        print("\nData Quality Metrics:")
        print("-" * 30)
        print(f"Negative Predictions: {metrics.get('negative_preds', 0)}")
        print(f"Zero Predictions: {metrics.get('zero_preds', 0)}")
        print(f"Prediction Range: {metrics.get('pred_range', 0):,.2f}")
        print(f"Actual Range: {metrics.get('actual_range', 0):,.2f}")


def train_advanced_model(max_epochs=1000, cv_epochs=1000):
    """Train advanced neural network with comprehensive evaluation

    Args:
        max_epochs (int): Maximum number of training epochs for main model
        cv_epochs (int): Number of epochs for cross-validation training
    """

    print("Starting Advanced Query Time Prediction Training")
    print("=" * 60)
    print(f"Configuration: Main training epochs = {max_epochs}, CV epochs = {cv_epochs}")

    # Load and preprocess data with advanced features
    preprocessor = ImprovedDataPreprocessor(
        "Dataset/Dataset/train/train.csv",
        "Dataset/Dataset/test/test.csv",
        use_feature_engineering=True
    )
    (X_train, y_train, X_val, y_val, X_test, y_test_log,
     y_train_orig, y_val_orig, y_test_orig) = preprocessor.load_and_preprocess()

    # Create advanced model using MSE loss
    input_size = X_train.shape[1]
    model = AdvancedQueryPredictor(
        input_size=input_size,
        hidden_sizes=[128, 64, 32],
        dropout_rate=0.3
    )

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)

    print(f"\nAdvanced Model Architecture:")
    print(f"Input size: {input_size}")
    print(f"Hidden layers: {model.hidden_sizes}")
    print(f"Loss function: MSE")
    print(f"Device: {device}")
    print(f"Total parameters: {sum(p.numel() for p in model.parameters()):,}")

    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.FloatTensor(y_train).to(device)
    X_val_tensor = torch.FloatTensor(X_val).to(device)
    y_val_tensor = torch.FloatTensor(y_val).to(device)

    # Dynamic batch size based on dataset size
    batch_size = min(64, max(16, len(X_train) // 20))
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

    print(f"Training configuration:")
    print(f"Batch size: {batch_size}")
    print(f"Training batches: {len(train_loader)}")

    # Advanced optimizer with better learning rate
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=10, T_mult=2)

    # Use MSE loss for regression
    criterion = nn.MSELoss()

    # Initialize evaluator
    evaluator = ComprehensiveEvaluator()

    # Training tracking
    train_losses = []
    val_losses = []
    val_metrics_history = []
    best_val_loss = float('inf')
    best_metrics = None
    patience_counter = 0

    print("\nStarting advanced training with MSE loss...")
    print("Epoch | Train Loss | Val Loss   | R²     | RMSE    | LR        | Status")
    print("-" * 75)

    for epoch in range(max_epochs):  # Configurable epochs
        # Training phase
        model.train()
        train_loss = 0.0
        batch_count = 0

        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()

            # Forward pass
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)

            # Check for NaN loss
            if torch.isnan(loss):
                print(f"NaN loss detected at epoch {epoch}, batch {batch_count}")
                break

            # Backward pass with improved gradient clipping
            loss.backward()

            # More aggressive gradient clipping for stability
            grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)

            # Check gradients and skip update if too large
            if grad_norm > 5.0:
                if grad_norm > 50.0:
                    print(f"Skipping update due to extremely large gradient: {grad_norm:.3f}")
                    optimizer.zero_grad()
                    continue
                elif batch_count % 10 == 0:  # Reduce logging frequency
                    print(f"Large gradient norm: {grad_norm:.3f}")

            # Check for NaN gradients
            has_nan_grad = False
            for param in model.parameters():
                if param.grad is not None and torch.isnan(param.grad).any():
                    has_nan_grad = True
                    break

            if has_nan_grad:
                print(f"NaN gradients detected at epoch {epoch}, batch {batch_count}")
                optimizer.zero_grad()
                continue

            optimizer.step()
            train_loss += loss.item()
            batch_count += 1

        # Validation phase
        model.eval()
        with torch.no_grad():
            val_outputs = model(X_val_tensor)
            val_loss = criterion(val_outputs, y_val_tensor).item()

        # Comprehensive evaluation every 10 epochs
        if epoch % 10 == 0 or epoch < 10:
            val_metrics, val_pred_orig, val_pred_std = evaluator.evaluate_model(
                model, X_val, y_val, y_val_orig, preprocessor, device
            )
            val_metrics_history.append(val_metrics)
        else:
            val_metrics = val_metrics_history[-1] if val_metrics_history else {}

        # Update learning rate
        scheduler.step()

        # Record losses
        avg_train_loss = train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        val_losses.append(val_loss)

        # Track best model based on validation loss
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_metrics = val_metrics.copy() if val_metrics else None
            torch.save(model.state_dict(), 'best_advanced_model.pth')
            patience_counter = 0
            status = "✓"
        else:
            patience_counter += 1
            status = ""

        # Early stopping with adaptive patience based on max_epochs
        patience_limit = min(100, max_epochs // 10)  # 10% of max epochs or 100, whichever is smaller
        if patience_counter >= patience_limit:
            print(f"\nEarly stopping at epoch {epoch} (patience: {patience_limit})")
            break

        # Print progress
        current_lr = optimizer.param_groups[0]['lr']

        if epoch % 10 == 0 or epoch < 10:
            r2_val = val_metrics.get('r2', 0.0)
            rmse_val = val_metrics.get('rmse', 0.0)
            print(f"{epoch:5d} | {avg_train_loss:10.6f} | {val_loss:10.6f} | {r2_val:6.4f} | {rmse_val:7.1f} | {current_lr:.2e} | {status}")

    # Load best model
    model.load_state_dict(torch.load('best_advanced_model.pth'))

    # Final comprehensive evaluation
    print(f"\nTraining completed!")
    print(f"Best validation loss: {best_val_loss:.6f}")

    final_metrics, val_pred_orig, val_pred_std = evaluator.evaluate_model(
        model, X_val, y_val, y_val_orig, preprocessor, device
    )

    evaluator.print_metrics(final_metrics, "Final Validation Results")

    # Cross-validation evaluation
    print("\nPerforming cross-validation evaluation...")
    cv_metrics = perform_cross_validation(model, X_train, y_train, preprocessor, device, cv_epochs=cv_epochs)
    evaluator.print_metrics(cv_metrics, "Cross-Validation Results")

    # Generate test predictions
    if y_test_orig is None:
        print("\nGenerating test predictions...")
        test_predictions = generate_test_predictions(model, X_test, preprocessor, device)

        # Save predictions
        test_df = pd.read_csv("Dataset/Dataset/test/test.csv")
        test_df['QueryTime_Predicted'] = test_predictions['mean']

        output_file = 'advanced_test_predictions.csv'
        test_df.to_csv(output_file, index=False)
        print(f"Test predictions saved to {output_file}")
        print(f"Test prediction range: {test_predictions['mean'].min():.2f} to {test_predictions['mean'].max():.2f}")

    # Generate comprehensive visualizations
    create_comprehensive_plots(train_losses, val_losses, val_metrics_history,
                             y_val_orig, val_pred_orig, val_pred_std)

    return model, preprocessor, train_losses, val_losses, final_metrics


def perform_cross_validation(model, X_train, y_train, preprocessor, device, k_folds=5, cv_epochs=1000):
    """Perform k-fold cross-validation with configurable epochs"""

    # Convert back to numpy for sklearn
    if torch.is_tensor(X_train):
        X_train = X_train.cpu().numpy()
    if torch.is_tensor(y_train):
        y_train = y_train.cpu().numpy()

    kfold = StratifiedKFold(n_splits=k_folds, shuffle=True, random_state=42)

    # Create stratification labels based on target quantiles
    y_flat = y_train.flatten()
    stratify_labels = pd.qcut(y_flat, q=k_folds, labels=False, duplicates='drop')

    cv_metrics = []

    for fold, (train_idx, val_idx) in enumerate(kfold.split(X_train, stratify_labels)):
        print(f"Cross-validation fold {fold + 1}/{k_folds} (Training for {cv_epochs} epochs)")

        # Split data
        X_fold_train, X_fold_val = X_train[train_idx], X_train[val_idx]
        y_fold_train, y_fold_val = y_train[train_idx], y_train[val_idx]

        # Convert to tensors
        X_fold_train_tensor = torch.FloatTensor(X_fold_train).to(device)
        y_fold_train_tensor = torch.FloatTensor(y_fold_train).to(device)

        # Training for this fold with configurable epochs
        model.train()
        fold_optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
        fold_scheduler = optim.lr_scheduler.ReduceLROnPlateau(fold_optimizer, patience=50, factor=0.5)

        fold_criterion = nn.MSELoss()

        # Training loop with early stopping for CV
        best_fold_loss = float('inf')
        patience_counter = 0
        patience_limit = min(100, cv_epochs // 10)  # Adaptive patience based on total epochs

        for epoch in range(cv_epochs):
            fold_optimizer.zero_grad()

            outputs = model(X_fold_train_tensor)
            loss = fold_criterion(outputs, y_fold_train_tensor)

            # Skip if loss is NaN
            if torch.isnan(loss):
                print(f"NaN loss in CV fold {fold + 1}, epoch {epoch}")
                continue

            loss.backward()

            # Gradient clipping for stability
            grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)

            # Skip update if gradients are too large
            if grad_norm > 50.0:
                fold_optimizer.zero_grad()
                continue

            fold_optimizer.step()

            # Learning rate scheduling
            fold_scheduler.step(loss.item())

            # Early stopping check
            if loss.item() < best_fold_loss:
                best_fold_loss = loss.item()
                patience_counter = 0
            else:
                patience_counter += 1

            if patience_counter >= patience_limit:
                print(f"Early stopping in CV fold {fold + 1} at epoch {epoch}")
                break

            # Progress reporting for longer training
            if cv_epochs >= 100 and epoch % (cv_epochs // 10) == 0:
                print(f"  Fold {fold + 1}, Epoch {epoch}/{cv_epochs}, Loss: {loss.item():.6f}")

        # Evaluate fold
        y_fold_val_orig = np.expm1(preprocessor.target_scaler.inverse_transform(y_fold_val).flatten())
        evaluator = ComprehensiveEvaluator()
        fold_metrics, _, _ = evaluator.evaluate_model(
            model, X_fold_val, y_fold_val, y_fold_val_orig, preprocessor, device
        )

        cv_metrics.append(fold_metrics)
        print(f"Fold {fold + 1} completed - R²: {fold_metrics.get('r2', 0):.4f}, RMSE: {fold_metrics.get('rmse', 0):,.0f}")

    # Average metrics across folds
    avg_metrics = {}
    for key in cv_metrics[0].keys():
        if isinstance(cv_metrics[0][key], (int, float)):
            avg_metrics[key] = np.mean([m[key] for m in cv_metrics])
            avg_metrics[f'{key}_std'] = np.std([m[key] for m in cv_metrics])

    return avg_metrics


def generate_test_predictions(model, X_test, preprocessor, device):
    """Generate test predictions using MSE model"""

    model.eval()

    with torch.no_grad():
        X_test_tensor = torch.FloatTensor(X_test).to(device)

        test_pred = model(X_test_tensor).cpu().numpy()
        test_pred_log = preprocessor.target_scaler.inverse_transform(test_pred)
        test_pred_orig = np.expm1(test_pred_log.flatten())
        test_pred_orig = np.maximum(test_pred_orig, 0)

        predictions = {'mean': test_pred_orig}

    return predictions


def create_comprehensive_plots(train_losses, val_losses, val_metrics_history,
                             y_val_orig, val_pred_orig, val_pred_std=None):
    """Create comprehensive visualization plots with proper scaling for extreme values"""

    # Handle extreme values and outliers for better visualization
    def remove_outliers(data, percentile=99):
        """Remove extreme outliers for better visualization"""
        upper_limit = np.percentile(data, percentile)
        lower_limit = np.percentile(data, 100 - percentile)
        return np.clip(data, lower_limit, upper_limit)

    # Clean data for visualization
    y_val_clean = remove_outliers(y_val_orig, 95)
    val_pred_clean = remove_outliers(val_pred_orig, 95)

    # Create figure with better spacing - expanded to 4x3 for additional metrics
    fig = plt.figure(figsize=(24, 18))
    gs = fig.add_gridspec(4, 3, hspace=0.3, wspace=0.3)

    # 1. Training curves with better scaling
    ax1 = fig.add_subplot(gs[0, 0])

    # Smooth training curves for better visualization
    if len(train_losses) > 10:
        # Apply moving average for smoother curves
        window = min(10, len(train_losses) // 10)
        train_smooth = np.convolve(train_losses, np.ones(window)/window, mode='valid')
        val_smooth = np.convolve(val_losses, np.ones(window)/window, mode='valid')
        epochs_smooth = np.arange(window-1, len(train_losses))

        ax1.plot(epochs_smooth, train_smooth, label='Training Loss (Smoothed)', alpha=0.8, color='blue', linewidth=2)
        ax1.plot(epochs_smooth, val_smooth, label='Validation Loss (Smoothed)', alpha=0.8, color='orange', linewidth=2)

        # Add raw data as lighter lines
        ax1.plot(train_losses, alpha=0.3, color='blue', linewidth=1)
        ax1.plot(val_losses, alpha=0.3, color='orange', linewidth=1)
    else:
        ax1.plot(train_losses, label='Training Loss', alpha=0.8, color='blue')
        ax1.plot(val_losses, label='Validation Loss', alpha=0.8, color='orange')

    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss (Scaled)')
    ax1.set_title('Training Progress (Smoothed)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_yscale('symlog')  # Better for negative values

    # 2. Predictions vs Actual with improved scaling
    ax2 = fig.add_subplot(gs[0, 1])

    # Calculate R² for the plot
    r2_val = r2_score(y_val_orig, val_pred_orig)

    # Use cleaned data for scatter plot
    scatter_plot = ax2.scatter(y_val_clean, val_pred_clean, alpha=0.6, s=30, c='blue', edgecolors='white', linewidth=0.5)

    # Perfect prediction line
    min_val = min(y_val_clean.min(), val_pred_clean.min())
    max_val = max(y_val_clean.max(), val_pred_clean.max())
    ax2.plot([min_val, max_val], [min_val, max_val], 'r--', lw=2, label='Perfect Prediction')

    ax2.set_xlabel('Actual Query Time (ms)')
    ax2.set_ylabel('Predicted Query Time (ms)')
    ax2.set_title(f'Predictions vs Actual (R² = {r2_val:.4f})')

    # Use log scale only if data spans multiple orders of magnitude
    if max_val / min_val > 100:
        ax2.set_xscale('log')
        ax2.set_yscale('log')

    ax2.grid(True, alpha=0.3)
    ax2.legend()

    # Add correlation info
    pearson_corr = np.corrcoef(y_val_orig, val_pred_orig)[0, 1]
    ax2.text(0.05, 0.95, f'Pearson r = {pearson_corr:.3f}', transform=ax2.transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

    # 3. Metrics evolution
    ax3 = fig.add_subplot(gs[0, 2])
    if val_metrics_history:
        epochs_metrics = np.arange(0, len(val_metrics_history) * 10, 10)
        r2_history = [m.get('r2', 0) for m in val_metrics_history]
        rmse_history = [m.get('rmse', 0) for m in val_metrics_history]

        ax_r2 = ax3
        ax_rmse = ax_r2.twinx()

        line1 = ax_r2.plot(epochs_metrics, r2_history, 'b-', label='R²', alpha=0.8, linewidth=2)
        line2 = ax_rmse.plot(epochs_metrics, rmse_history, 'r-', label='RMSE', alpha=0.8, linewidth=2)

        ax_r2.set_xlabel('Epoch')
        ax_r2.set_ylabel('R²', color='b')
        ax_rmse.set_ylabel('RMSE', color='r')
        ax_r2.set_title('Metrics Evolution')
        ax_r2.grid(True, alpha=0.3)
        ax_r2.set_ylim(0, 1)  # R² should be between 0 and 1

        # Combine legends
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax_r2.legend(lines, labels, loc='center right')
    else:
        ax3.text(0.5, 0.5, 'Metrics Evolution\n(No history available)',
                ha='center', va='center', transform=ax3.transAxes,
                fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        ax3.set_title('Metrics Evolution')

    # 4. Residuals plot with improved scaling
    ax4 = fig.add_subplot(gs[1, 0])
    residuals = y_val_orig - val_pred_orig
    residuals_clean = remove_outliers(residuals, 95)
    pred_clean_for_residuals = remove_outliers(val_pred_orig, 95)

    ax4.scatter(pred_clean_for_residuals, residuals_clean, alpha=0.6, s=30, c='green', edgecolors='white', linewidth=0.5)
    ax4.axhline(y=0, color='r', linestyle='--', linewidth=2, label='Zero Line')
    ax4.set_xlabel('Predicted Query Time (ms)')
    ax4.set_ylabel('Residuals (ms)')
    ax4.set_title('Residual Plot (Outliers Removed)')
    ax4.grid(True, alpha=0.3)

    # Add residual statistics
    residual_std = np.std(residuals_clean)
    ax4.axhline(y=2*residual_std, color='orange', linestyle=':', alpha=0.7, label='±2σ')
    ax4.axhline(y=-2*residual_std, color='orange', linestyle=':', alpha=0.7)
    ax4.legend()

    # Add RMSE text
    rmse_val = np.sqrt(np.mean(residuals**2))
    ax4.text(0.05, 0.95, f'RMSE = {rmse_val:,.0f}', transform=ax4.transAxes,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

    # 5. Error distribution with better binning
    ax5 = fig.add_subplot(gs[1, 1])

    # Use cleaned residuals for better visualization
    n_bins = min(50, max(10, len(residuals_clean) // 20))
    counts, bins, patches = ax5.hist(residuals_clean, bins=n_bins, alpha=0.7, color='purple',
                                    edgecolor='black', density=True)
    ax5.axvline(x=0, color='r', linestyle='--', linewidth=2, label='Zero')
    ax5.set_xlabel('Residuals (ms)')
    ax5.set_ylabel('Density')
    ax5.set_title('Residual Distribution')
    ax5.grid(True, alpha=0.3)

    # Add normal distribution overlay
    mu, sigma = stats.norm.fit(residuals_clean)
    x = np.linspace(residuals_clean.min(), residuals_clean.max(), 100)
    y = stats.norm.pdf(x, mu, sigma)
    ax5.plot(x, y, 'r-', linewidth=2, label=f'Normal(μ={mu:.0f}, σ={sigma:.0f})')
    ax5.legend()

    # Add skewness and kurtosis
    skewness = stats.skew(residuals_clean)
    kurtosis = stats.kurtosis(residuals_clean)
    ax5.text(0.05, 0.95, f'Skew: {skewness:.2f}\nKurt: {kurtosis:.2f}',
             transform=ax5.transAxes, bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

    # 6. Prediction intervals or Performance metrics
    ax6 = fig.add_subplot(gs[1, 2])

    if val_pred_std is not None and np.any(val_pred_std > 0):
        # Prediction interval calibration plot
        coverage_data = []
        confidence_levels = np.arange(0.1, 1.0, 0.1)

        for conf in confidence_levels:
            z_score = stats.norm.ppf((1 + conf) / 2)
            lower = val_pred_orig - z_score * val_pred_std.flatten()
            upper = val_pred_orig + z_score * val_pred_std.flatten()
            coverage = np.mean((y_val_orig >= lower) & (y_val_orig <= upper))
            coverage_data.append(coverage)

        ax6.plot(confidence_levels, coverage_data, 'bo-', label='Actual Coverage', linewidth=2, markersize=6)
        ax6.plot(confidence_levels, confidence_levels, 'r--', label='Perfect Coverage', linewidth=2)
        ax6.set_xlabel('Confidence Level')
        ax6.set_ylabel('Actual Coverage')
        ax6.set_title('Prediction Interval Calibration')
        ax6.grid(True, alpha=0.3)
        ax6.legend()
        ax6.set_xlim(0, 1)
        ax6.set_ylim(0, 1)
    else:
        # Enhanced performance metrics summary with new metrics
        # Calculate the new metrics for display
        y_mean = np.mean(y_val_orig)

        # RAE calculation
        numerator_rae = np.sum(np.abs(y_val_orig - val_pred_orig))
        denominator_rae = np.sum(np.abs(y_val_orig - y_mean))
        rae = numerator_rae / denominator_rae if denominator_rae > 0 else float('inf')

        # RRSE calculation
        numerator_rrse = np.sum((y_val_orig - val_pred_orig) ** 2)
        denominator_rrse = np.sum((y_val_orig - y_mean) ** 2)
        rrse = np.sqrt(numerator_rrse / denominator_rrse) if denominator_rrse > 0 else float('inf')

        # PR calculation
        pr = np.mean((y_val_orig - val_pred_orig) ** 2)

        # Bias calculation
        bias = np.mean(val_pred_orig - y_val_orig)
        bias_percentage = (bias / y_mean) * 100 if y_mean > 0 else 0

        metrics_text = f"""Enhanced Performance Summary

Basic Metrics:
R² Score: {r2_val:.4f}
RMSE: {np.sqrt(np.mean(residuals**2)):,.0f}
MAE: {np.mean(np.abs(residuals)):,.0f}
MAPE: {np.mean(np.abs(residuals/y_val_orig))*100:.1f}%

Advanced Metrics:
RAE: {rae:.4f}
RRSE: {rrse:.4f}
PR: {pr:,.0f}

Bias Analysis:
Bias: {bias:,.0f}
Bias %: {bias_percentage:.2f}%

Correlations:
Pearson r: {pearson_corr:.3f}
Spearman ρ: {stats.spearmanr(y_val_orig, val_pred_orig)[0]:.3f}

Data Quality:
Points: {len(y_val_orig):,}
Outliers: {len(y_val_orig) - len(y_val_clean):,}"""

        ax6.text(0.05, 0.95, metrics_text, transform=ax6.transAxes, fontsize=9,
                verticalalignment='top', bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
        ax6.set_title('Enhanced Performance Summary')
        ax6.axis('off')

    # 7. Learning curves (last 50% of training)
    ax7 = fig.add_subplot(gs[2, 0])

    if len(train_losses) > 20:
        start_idx = len(train_losses) // 2  # Last 50% of training
        epochs_subset = np.arange(start_idx, len(train_losses))

        ax7.plot(epochs_subset, train_losses[start_idx:], label='Training Loss', alpha=0.8, color='blue', linewidth=2)
        ax7.plot(epochs_subset, val_losses[start_idx:], label='Validation Loss', alpha=0.8, color='orange', linewidth=2)
        ax7.set_xlabel('Epoch')
        ax7.set_ylabel('Loss')
        ax7.set_title(f'Learning Curves (Last {len(train_losses) - start_idx} Epochs)')
        ax7.legend()
        ax7.grid(True, alpha=0.3)
        ax7.set_yscale('symlog')
    else:
        ax7.text(0.5, 0.5, 'Insufficient training\nhistory for\nlearning curves',
                ha='center', va='center', transform=ax7.transAxes,
                fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        ax7.set_title('Learning Curves')

    # 8. Prediction error by magnitude
    ax8 = fig.add_subplot(gs[2, 1])

    # Bin predictions by magnitude and show error statistics
    y_val_log = np.log10(np.maximum(y_val_orig, 1))  # Avoid log(0)
    bins = np.linspace(y_val_log.min(), y_val_log.max(), 10)
    bin_centers = (bins[:-1] + bins[1:]) / 2

    relative_errors = np.abs(residuals) / y_val_orig
    bin_indices = np.digitize(y_val_log, bins) - 1
    bin_indices = np.clip(bin_indices, 0, len(bin_centers) - 1)

    mean_errors = []
    std_errors = []

    for i in range(len(bin_centers)):
        mask = bin_indices == i
        if mask.sum() > 0:
            mean_errors.append(np.mean(relative_errors[mask]))
            std_errors.append(np.std(relative_errors[mask]))
        else:
            mean_errors.append(0)
            std_errors.append(0)

    ax8.errorbar(10**bin_centers, mean_errors, yerr=std_errors,
                marker='o', capsize=5, capthick=2, linewidth=2)
    ax8.set_xlabel('Query Time (ms)')
    ax8.set_ylabel('Relative Error')
    ax8.set_title('Error vs Query Magnitude')
    ax8.set_xscale('log')
    ax8.grid(True, alpha=0.3)

    # 9. Training stability metrics
    ax9 = fig.add_subplot(gs[2, 2])

    if len(train_losses) > 10:
        # Calculate training stability metrics
        loss_diff = np.diff(val_losses)
        stability_window = min(20, len(loss_diff) // 4)

        if stability_window > 5:
            stability_metric = []
            for i in range(stability_window, len(loss_diff)):
                window_std = np.std(loss_diff[i-stability_window:i])
                stability_metric.append(window_std)

            epochs_stability = np.arange(stability_window, len(loss_diff))
            ax9.plot(epochs_stability, stability_metric, color='purple', linewidth=2)
            ax9.set_xlabel('Epoch')
            ax9.set_ylabel('Loss Volatility')
            ax9.set_title('Training Stability')
            ax9.grid(True, alpha=0.3)
            ax9.set_yscale('log')
        else:
            ax9.text(0.5, 0.5, 'Insufficient data\nfor stability\nanalysis',
                    ha='center', va='center', transform=ax9.transAxes,
                    fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
            ax9.set_title('Training Stability')
    else:
        ax9.text(0.5, 0.5, 'Training Stability\n(Insufficient data)',
                ha='center', va='center', transform=ax9.transAxes,
                fontsize=12, bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray"))
        ax9.set_title('Training Stability')

    # 10. Advanced Metrics Visualization (RAE, RRSE, PR)
    ax10 = fig.add_subplot(gs[3, 0])

    # Calculate advanced metrics for visualization
    y_mean = np.mean(y_val_orig)

    # RAE calculation
    numerator_rae = np.sum(np.abs(y_val_orig - val_pred_orig))
    denominator_rae = np.sum(np.abs(y_val_orig - y_mean))
    rae = numerator_rae / denominator_rae if denominator_rae > 0 else 0

    # RRSE calculation
    numerator_rrse = np.sum((y_val_orig - val_pred_orig) ** 2)
    denominator_rrse = np.sum((y_val_orig - y_mean) ** 2)
    rrse = np.sqrt(numerator_rrse / denominator_rrse) if denominator_rrse > 0 else 0

    # PR calculation
    pr = np.mean((y_val_orig - val_pred_orig) ** 2)

    # Normalized PR for better visualization
    pr_normalized = pr / (y_mean ** 2) if y_mean > 0 else 0

    # Create bar plot for advanced metrics
    metrics_names = ['RAE', 'RRSE', 'PR (Normalized)']
    metrics_values = [rae, rrse, pr_normalized]
    colors = ['skyblue', 'lightcoral', 'lightgreen']

    bars = ax10.bar(metrics_names, metrics_values, color=colors, alpha=0.7, edgecolor='black')
    ax10.set_ylabel('Metric Value')
    ax10.set_title('Advanced Evaluation Metrics\n(RAE, RRSE, Normalized PR)')
    ax10.grid(True, alpha=0.3, axis='y')

    # Add value labels on bars
    for bar, value in zip(bars, metrics_values):
        height = bar.get_height()
        ax10.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                 f'{value:.4f}', ha='center', va='bottom', fontweight='bold')

    # Add reference lines
    ax10.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='Perfect Score (1.0)')
    ax10.legend()

    # 11. Metrics Comparison Chart
    ax11 = fig.add_subplot(gs[3, 1])

    # Compare different error metrics
    rmse_val = np.sqrt(np.mean(residuals**2))
    mae_val = np.mean(np.abs(residuals))
    mape_val = np.mean(np.abs(residuals/y_val_orig))*100

    # Normalize metrics for comparison (0-1 scale)
    max_rmse = y_val_orig.max()
    max_mae = y_val_orig.max()

    normalized_metrics = {
        'RMSE': rmse_val / max_rmse,
        'MAE': mae_val / max_mae,
        'RAE': min(rae, 2.0) / 2.0,  # Cap at 2.0 for visualization
        'RRSE': min(rrse, 2.0) / 2.0,  # Cap at 2.0 for visualization
        'MAPE': min(mape_val, 100) / 100  # Cap at 100% for visualization
    }

    # Create radar-like comparison
    metric_names = list(normalized_metrics.keys())
    metric_values = list(normalized_metrics.values())

    bars = ax11.barh(metric_names, metric_values, color='lightsteelblue', alpha=0.7, edgecolor='navy')
    ax11.set_xlabel('Normalized Error (0=Perfect, 1=Poor)')
    ax11.set_title('Error Metrics Comparison\n(Normalized Scale)')
    ax11.grid(True, alpha=0.3, axis='x')
    ax11.set_xlim(0, 1)

    # Add value labels
    for bar, value in zip(bars, metric_values):
        width = bar.get_width()
        ax11.text(width + 0.02, bar.get_y() + bar.get_height()/2.,
                 f'{value:.3f}', ha='left', va='center', fontweight='bold')

    # 12. Prediction Quality Assessment
    ax12 = fig.add_subplot(gs[3, 2])

    # Quality metrics visualization
    quality_metrics = {
        'R² Score': r2_val,
        'Pearson r': pearson_corr,
        'Spearman ρ': stats.spearmanr(y_val_orig, val_pred_orig)[0],
        '1 - RAE': max(0, 1 - rae),  # Higher is better
        '1 - RRSE': max(0, 1 - rrse)  # Higher is better
    }

    # Create pie chart for quality assessment
    quality_names = list(quality_metrics.keys())
    quality_values = [max(0, min(1, v)) for v in quality_metrics.values()]  # Ensure 0-1 range

    # Color scheme: green for good, yellow for moderate, red for poor
    colors_quality = []
    for val in quality_values:
        if val >= 0.8:
            colors_quality.append('lightgreen')
        elif val >= 0.6:
            colors_quality.append('gold')
        elif val >= 0.4:
            colors_quality.append('orange')
        else:
            colors_quality.append('lightcoral')

    wedges, texts, autotexts = ax12.pie(quality_values, labels=quality_names, colors=colors_quality,
                                       autopct='%1.3f', startangle=90, textprops={'fontsize': 9})
    ax12.set_title('Model Quality Assessment\n(Higher Values = Better Performance)')

    # Add quality legend
    quality_legend = ['Excellent (≥0.8)', 'Good (≥0.6)', 'Fair (≥0.4)', 'Poor (<0.4)']
    legend_colors = ['lightgreen', 'gold', 'orange', 'lightcoral']
    ax12.legend(quality_legend, loc='center left', bbox_to_anchor=(1, 0, 0.5, 1),
               title='Quality Scale', title_fontsize=10)

    plt.tight_layout()

    # Save with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f'improved_training_results_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"Comprehensive plots saved to {filename}")
    plt.show()


if __name__ == "__main__":
    print("Starting Advanced Neural Network Training for Query Time Prediction")
    print("=" * 70)

    # Configuration
    MAIN_EPOCHS = 1000  # Main training epochs
    CV_EPOCHS = 1000    # Cross-validation epochs

    print(f"Training Configuration:")
    print(f"Main training epochs: {MAIN_EPOCHS}")
    print(f"Cross-validation epochs: {CV_EPOCHS}")
    print("=" * 70)

    try:
        model, preprocessor, train_losses, val_losses, final_metrics = train_advanced_model(
            max_epochs=MAIN_EPOCHS,
            cv_epochs=CV_EPOCHS
        )

        print("\n" + "=" * 70)
        print("TRAINING COMPLETED SUCCESSFULLY!")
        print("=" * 70)

        # Save model and results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save model
        torch.save({
            'model_state_dict': model.state_dict(),
            'model_config': {
                'input_size': model.input_size,
                'hidden_sizes': model.hidden_sizes,
                'loss_function': 'MSE'
            },
            'final_metrics': final_metrics,
            'training_history': {
                'train_losses': train_losses,
                'val_losses': val_losses
            }
        }, f'advanced_query_predictor_{timestamp}.pth')

        # Save metrics to JSON
        metrics_file = f'training_metrics_{timestamp}.json'
        with open(metrics_file, 'w') as f:
            json.dump(final_metrics, f, indent=2, default=str)

        print(f"Model saved to: advanced_query_predictor_{timestamp}.pth")
        print(f"Metrics saved to: {metrics_file}")

        # Print final summary
        print(f"\nFinal Model Performance:")
        print(f"R² Score: {final_metrics.get('r2', 0):.4f}")
        print(f"RMSE: {final_metrics.get('rmse', 0):,.2f}")
        print(f"MAE: {final_metrics.get('mae', 0):,.2f}")
        print(f"MAPE: {final_metrics.get('mape', 0):.2f}%")

        if 'coverage_95' in final_metrics:
            print(f"95% Coverage: {final_metrics['coverage_95']:.3f}")
            print(f"Uncertainty Quality: {final_metrics.get('uncertainty_quality', 0):.3f}")

    except Exception as e:
        print(f"Error during training: {str(e)}")
        import traceback
        traceback.print_exc()


