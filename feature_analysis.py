import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import RobustScaler, StandardScaler

def analyze_feature_scaling_issues():
    """Analyze scaling differences between train and test sets"""
    
    print("=== FEATURE SCALING ANALYSIS ===")
    
    # Load both datasets
    train_df = pd.read_csv("Dataset/Dataset/train/train.csv")
    test_df = pd.read_csv("Dataset/Dataset/test/test.csv")
    
    # Get feature columns (exclude QueryTime)
    feature_columns = [col for col in train_df.columns if col != 'QueryTime']
    
    train_features = train_df[feature_columns]
    test_features = test_df[feature_columns]
    
    print(f"Train features shape: {train_features.shape}")
    print(f"Test features shape: {test_features.shape}")
    
    # Check for scaling issues
    scaling_issues = []
    
    for col in feature_columns:
        train_col = train_features[col]
        test_col = test_features[col]
        
        # Basic statistics
        train_mean = train_col.mean()
        test_mean = test_col.mean()
        train_std = train_col.std()
        test_std = test_col.std()
        train_min = train_col.min()
        test_min = test_col.min()
        train_max = train_col.max()
        test_max = test_col.max()
        
        # Check for significant differences
        mean_ratio = abs(train_mean - test_mean) / (abs(train_mean) + 1e-8)
        std_ratio = abs(train_std - test_std) / (abs(train_std) + 1e-8)
        range_ratio = abs((train_max - train_min) - (test_max - test_min)) / (abs(train_max - train_min) + 1e-8)
        
        # Flag problematic features
        if mean_ratio > 0.5 or std_ratio > 0.5 or range_ratio > 0.5:
            scaling_issues.append({
                'feature': col,
                'train_mean': train_mean,
                'test_mean': test_mean,
                'train_std': train_std,
                'test_std': test_std,
                'train_range': train_max - train_min,
                'test_range': test_max - test_min,
                'mean_ratio': mean_ratio,
                'std_ratio': std_ratio,
                'range_ratio': range_ratio
            })
    
    print(f"\nFound {len(scaling_issues)} features with scaling issues:")
    
    for issue in scaling_issues[:10]:  # Show first 10
        print(f"\nFeature: {issue['feature']}")
        print(f"  Train mean: {issue['train_mean']:.3f}, Test mean: {issue['test_mean']:.3f}")
        print(f"  Train std: {issue['train_std']:.3f}, Test std: {issue['test_std']:.3f}")
        print(f"  Train range: {issue['train_range']:.3f}, Test range: {issue['test_range']:.3f}")
    
    # Check for features that exist in one set but not the other
    train_only = set(train_features.columns) - set(test_features.columns)
    test_only = set(test_features.columns) - set(train_features.columns)
    
    if train_only:
        print(f"\nFeatures only in train: {train_only}")
    if test_only:
        print(f"\nFeatures only in test: {test_only}")
    
    # Check for different value ranges
    print(f"\nOverall statistics comparison:")
    print(f"Train features - Min: {train_features.min().min():.3f}, Max: {train_features.max().max():.3f}")
    print(f"Test features - Min: {test_features.min().min():.3f}, Max: {test_features.max().max():.3f}")
    
    # Plot comparison
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Feature means comparison
    train_means = train_features.mean()
    test_means = test_features.mean()
    
    axes[0,0].scatter(train_means, test_means, alpha=0.6)
    axes[0,0].plot([train_means.min(), train_means.max()], 
                   [train_means.min(), train_means.max()], 'r--')
    axes[0,0].set_xlabel('Train Feature Means')
    axes[0,0].set_ylabel('Test Feature Means')
    axes[0,0].set_title('Feature Means: Train vs Test')
    axes[0,0].grid(True, alpha=0.3)
    
    # Feature std comparison
    train_stds = train_features.std()
    test_stds = test_features.std()
    
    axes[0,1].scatter(train_stds, test_stds, alpha=0.6)
    axes[0,1].plot([train_stds.min(), train_stds.max()], 
                   [train_stds.min(), train_stds.max()], 'r--')
    axes[0,1].set_xlabel('Train Feature Std')
    axes[0,1].set_ylabel('Test Feature Std')
    axes[0,1].set_title('Feature Std: Train vs Test')
    axes[0,1].grid(True, alpha=0.3)
    
    # Distribution of scaling ratios
    mean_ratios = [abs(train_means[col] - test_means[col]) / (abs(train_means[col]) + 1e-8) 
                   for col in feature_columns]
    
    axes[1,0].hist(mean_ratios, bins=30, alpha=0.7)
    axes[1,0].set_xlabel('Mean Difference Ratio')
    axes[1,0].set_ylabel('Number of Features')
    axes[1,0].set_title('Distribution of Mean Differences')
    axes[1,0].grid(True, alpha=0.3)
    
    # Zero counts comparison
    train_zeros = (train_features == 0).sum()
    test_zeros = (test_features == 0).sum()
    
    axes[1,1].scatter(train_zeros, test_zeros, alpha=0.6)
    axes[1,1].plot([train_zeros.min(), train_zeros.max()], 
                   [train_zeros.min(), train_zeros.max()], 'r--')
    axes[1,1].set_xlabel('Train Zero Counts')
    axes[1,1].set_ylabel('Test Zero Counts')
    axes[1,1].set_title('Zero Counts: Train vs Test')
    axes[1,1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('feature_scaling_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return scaling_issues, train_features, test_features

if __name__ == "__main__":
    scaling_issues, train_features, test_features = analyze_feature_scaling_issues()