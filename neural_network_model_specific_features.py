import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler, RobustScaler, LabelEncoder, MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.feature_selection import mutual_info_regression, SelectKBest
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Tuple, Dict, List, Optional
import warnings
import time
import json
import os
from datetime import datetime
warnings.filterwarnings('ignore')

class AdvancedQueryPredictor(nn.Module):
    """Advanced neural network for query time prediction using MSE loss"""

    def __init__(self, input_size: int, hidden_sizes: List[int] = [128, 64, 32],
                 dropout_rate: float = 0.3):
        super(AdvancedQueryPredictor, self).__init__()

        self.input_size = input_size
        self.hidden_sizes = hidden_sizes

        # Input layer with batch normalization
        self.input_layer = nn.Sequential(
            nn.Linear(input_size, hidden_sizes[0]),
            #nn.BatchNorm1d(hidden_sizes[0]),
            nn.Tanh(),
            #nn.Dropout(dropout_rate)
        )

        # Hidden layers with residual connections
        self.hidden_layers = nn.ModuleList()
        for i in range(len(hidden_sizes) - 1):
            layer = nn.Sequential(
                nn.Linear(hidden_sizes[i], hidden_sizes[i + 1]),
                #nn.BatchNorm1d(hidden_sizes[i + 1]),
                nn.Tanh(),
                #nn.Dropout(dropout_rate)
            )
            self.hidden_layers.append(layer)

        # Single output layer for regression
        self.output_layer = nn.Linear(hidden_sizes[-1], 1)

        # Initialize weights properly
        self._initialize_weights()

    def _initialize_weights(self):
        """Xavier/Glorot initialization for better gradient flow"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.zeros_(module.bias)

    def forward(self, x):
        # Input layer
        x = self.input_layer(x)

        # Hidden layers with residual connections
        for i, layer in enumerate(self.hidden_layers):
            residual = x
            x = layer(x)

            # Add residual connection if dimensions match
            if x.shape == residual.shape and i > 0:
                x = x + residual

        return self.output_layer(x)


class AdvancedFeatureExtractor:
    """Enhanced feature extraction and engineering for query execution plans"""

    def __init__(self):
        self.scalers = {
            'numerical': RobustScaler(),
            'categorical': LabelEncoder(),
            'temporal': MinMaxScaler()
        }
        self.feature_selector = None
        self.selected_features = None

    def extract_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Extract advanced features from execution plan data"""

        # SPECIFIC FEATURES: Use exactly the 10 specified features from train2.csv
        specific_features = [
            'EstimatedTotalSubtreeCostHashMatch',
            'EstimateRowsHashMatch',
            'total_num_joins',
            'ClusteredIndexScanOpCount',
            'ClusteredIndexSeekOpCount',
            'SortOpCount',
            'total_estimated_cpu_cost ',  # Note: has trailing space in CSV
            'total_estimated_io_cost',
            'EstimateRowsSort',
            'HashMatchOpCount'
        ]
        
        print(f"Using SPECIFIC 10 features: {specific_features}")
        
        # Check which features are available in the dataset
        available_features = [f for f in specific_features if f in df.columns]
        missing_features = [f for f in specific_features if f not in df.columns]
        
        if missing_features:
            print(f"Warning: Missing features: {missing_features}")
        
        print(f"Available features ({len(available_features)}): {available_features}")
        
        # Extract only available specific features
        if available_features:
            feature_df = df[available_features].copy()
        else:
            raise ValueError("None of the specified features are available in the dataset!")
        
        # Handle missing values
        feature_df = feature_df.fillna(0)
        
        print(f"Final feature matrix shape: {feature_df.shape}")
        return feature_df

    def fit_transform(self, df: pd.DataFrame, target_col: str = 'QueryTime') -> Tuple[np.ndarray, np.ndarray]:
        """Fit transformers and transform the data"""
        
        # Extract features
        feature_df = self.extract_advanced_features(df)
        
        # Get target
        y = df[target_col].values
        
        # Scale features
        X_scaled = self.scalers['numerical'].fit_transform(feature_df)
        
        self.selected_features = feature_df.columns.tolist()
        
        return X_scaled, y

    def transform(self, df: pd.DataFrame) -> np.ndarray:
        """Transform new data using fitted transformers"""
        
        # Extract same features
        feature_df = self.extract_advanced_features(df)
        
        # Ensure same features as training
        if self.selected_features:
            # Reorder columns to match training
            feature_df = feature_df.reindex(columns=self.selected_features, fill_value=0)
        
        # Scale features
        X_scaled = self.scalers['numerical'].transform(feature_df)
        
        return X_scaled


def calculate_comprehensive_metrics(y_true, y_pred, y_std=None):
    """Calculate comprehensive evaluation metrics"""
    
    # Basic regression metrics
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)
    r2 = r2_score(y_true, y_pred)
    
    # Additional metrics
    mape = np.mean(np.abs((y_true - y_pred) / y_true)) * 100
    
    # Relative metrics
    rae = np.sum(np.abs(y_true - y_pred)) / np.sum(np.abs(y_true - np.mean(y_true)))
    rrse = np.sqrt(np.sum((y_true - y_pred) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2))
    
    # Prediction ratio
    pr = np.sum(y_pred) / np.sum(y_true) if np.sum(y_true) != 0 else 0
    
    # Normalized RMSE
    nrmse = rmse / (np.max(y_true) - np.min(y_true)) if np.max(y_true) != np.min(y_true) else 0
    
    # Correlation metrics
    pearson_corr = np.corrcoef(y_true, y_pred)[0, 1] if len(y_true) > 1 else 0
    spearman_corr = stats.spearmanr(y_true, y_pred)[0] if len(y_true) > 1 else 0
    
    metrics = {
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'mape': mape,
        'rae': rae,
        'rrse': rrse,
        'pr': pr,
        'nrmse': nrmse,
        'pearson_corr': pearson_corr,
        'spearman_corr': spearman_corr
    }
    
    return metrics

def print_metrics(metrics: Dict, title: str = "Model Performance"):
    """Print metrics in a formatted way"""
    print(f"\n{title}")
    print("=" * len(title))
    
    # Basic metrics
    print(f"Basic Regression Metrics:")
    print(f"  RMSE: {metrics['rmse']:,.2f}")
    print(f"  MAE: {metrics['mae']:,.2f}")
    print(f"  R²: {metrics['r2']:.4f}")
    print(f"  MAPE: {metrics['mape']:.2f}%")
    
    # Advanced metrics
    print(f"\nAdvanced Evaluation Metrics:")
    print(f"  RAE: {metrics['rae']:.4f}")
    print(f"  RRSE: {metrics['rrse']:.4f}")
    print(f"  PR: {metrics['pr']:,.2f}")
    print(f"  NRMSE: {metrics['nrmse']:.4f}")
    
    # Correlation metrics
    print(f"\nCorrelation Metrics:")
    print(f"  Pearson: {metrics['pearson_corr']:.4f}")
    print(f"  Spearman: {metrics['spearman_corr']:.4f}")


def evaluate_model_performance(model, X_val, y_val_orig, preprocessor, device):
    """Evaluate model performance with comprehensive metrics"""
    
    model.eval()
    with torch.no_grad():
        X_val_tensor = torch.FloatTensor(X_val).to(device)
        
        # Standard model prediction
        val_pred_scaled = model(X_val_tensor).cpu().numpy()
        val_pred_std = None
    
    # Inverse transform predictions
    val_pred_log = preprocessor.target_scaler.inverse_transform(val_pred_scaled)
    val_pred_orig = np.expm1(val_pred_log.flatten())
    val_pred_orig = np.maximum(val_pred_orig, 0)
    
    # Calculate comprehensive metrics
    metrics = calculate_comprehensive_metrics(y_val_orig, val_pred_orig, val_pred_std)
    
    return metrics, val_pred_orig


class DataPreprocessor:
    """Handle data preprocessing with proper scaling"""
    
    def __init__(self):
        self.feature_scaler = RobustScaler()
        self.target_scaler = StandardScaler()
        
    def fit_transform_features(self, X):
        return self.feature_scaler.fit_transform(X)
    
    def transform_features(self, X):
        return self.feature_scaler.transform(X)
    
    def fit_transform_target(self, y):
        y_log = np.log1p(y.values if hasattr(y, 'values') else y)
        return self.target_scaler.fit_transform(y_log.reshape(-1, 1)).flatten()

    def transform_target(self, y):
        y_log = np.log1p(y.values if hasattr(y, 'values') else y)
        return self.target_scaler.transform(y_log.reshape(-1, 1)).flatten()


def perform_cross_validation(X, y, y_orig, preprocessor, cv_folds=5, cv_epochs=200):
    """Perform cross-validation with the specific features"""
    
    print(f"\nPerforming {cv_folds}-fold cross-validation...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    kfold = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
    
    # Create stratification bins for continuous target
    y_bins = pd.qcut(y_orig, q=5, labels=False, duplicates='drop')
    
    cv_metrics = []
    
    for fold, (train_idx, val_idx) in enumerate(kfold.split(X, y_bins)):
        print(f"\nFold {fold + 1}/{cv_folds}")
        
        # Split data
        X_fold_train, X_fold_val = X[train_idx], X[val_idx]
        y_fold_train, y_fold_val = y[train_idx], y[val_idx]
        y_fold_val_orig = y_orig.iloc[val_idx]
        
        # Convert to tensors
        X_fold_train_tensor = torch.FloatTensor(X_fold_train).to(device)
        y_fold_train_tensor = torch.FloatTensor(y_fold_train).to(device)
        
        # Create model for this fold
        model = AdvancedQueryPredictor(
            input_size=X.shape[1],
            hidden_sizes=[128, 64, 32],
            dropout_rate=0.3
        ).to(device)
        
        # Training for this fold with configurable epochs
        model.train()
        fold_optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
        fold_scheduler = optim.lr_scheduler.ReduceLROnPlateau(fold_optimizer, patience=200, factor=0.9)
        
        fold_criterion = nn.MSELoss()
        
        best_val_loss = float('inf')
        patience_counter = 0
        patience = 200
        
        for epoch in range(cv_epochs):
            fold_optimizer.zero_grad()
            
            outputs = model(X_fold_train_tensor)
            loss = fold_criterion(outputs.squeeze(), y_fold_train_tensor)
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            fold_optimizer.step()
            
            # Validation
            if epoch % 10 == 0:
                model.eval()
                with torch.no_grad():
                    X_fold_val_tensor = torch.FloatTensor(X_fold_val).to(device)
                    val_outputs = model(X_fold_val_tensor)
                    val_loss = fold_criterion(val_outputs.squeeze(), torch.FloatTensor(y_fold_val).to(device))
                
                fold_scheduler.step(val_loss)
                
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                else:
                    patience_counter += 1
                
                if patience_counter >= patience:
                    break
                
                model.train()
        
        # Evaluate this fold
        fold_metrics, _ = evaluate_model_performance(model, X_fold_val, y_fold_val_orig, preprocessor, device)
        cv_metrics.append(fold_metrics)
        
        print(f"Fold {fold + 1} - R²: {fold_metrics['r2']:.4f}, RMSE: {fold_metrics['rmse']:,.2f}")
    
    # Calculate average metrics
    avg_metrics = {}
    for key in cv_metrics[0].keys():
        avg_metrics[key] = np.mean([m[key] for m in cv_metrics])
        avg_metrics[f'{key}_std'] = np.std([m[key] for m in cv_metrics])
    
    print(f"\nCross-Validation Results (Average ± Std):")
    print(f"R²: {avg_metrics['r2']:.4f} ± {avg_metrics['r2_std']:.4f}")
    print(f"RMSE: {avg_metrics['rmse']:,.2f} ± {avg_metrics['rmse_std']:,.2f}")
    print(f"MAE: {avg_metrics['mae']:,.2f} ± {avg_metrics['mae_std']:,.2f}")
    
    return avg_metrics


def generate_test_predictions(model, X_test, preprocessor, device):
    """Generate predictions for test set"""

    model.eval()
    with torch.no_grad():
        X_test_tensor = torch.FloatTensor(X_test).to(device)
        test_pred_scaled = model(X_test_tensor).cpu().numpy()

    # Inverse transform predictions
    test_pred_log = preprocessor.target_scaler.inverse_transform(test_pred_scaled)
    test_pred_orig = np.expm1(test_pred_log.flatten())
    test_pred_orig = np.maximum(test_pred_orig, 0)

    return {'mean': test_pred_orig}


def create_comprehensive_visualizations(train_losses, val_losses, val_r2_scores, val_rmse_scores,
                                       y_val_orig, val_pred_orig, final_metrics, timestamp):
    """Create comprehensive 12-panel visualization"""

    print("Creating comprehensive visualizations...")

    # Set up the plotting style
    plt.style.use('default')
    sns.set_palette("husl")

    # Create figure with 12 subplots (4 rows, 3 columns)
    fig = plt.figure(figsize=(20, 24))

    # 1. Training Progress (Loss)
    ax1 = plt.subplot(4, 3, 1)
    epochs = range(len(train_losses))
    plt.plot(epochs, train_losses, 'b-', label='Training Loss', alpha=0.8)
    plt.plot(epochs, val_losses, 'r-', label='Validation Loss', alpha=0.8)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training Progress - Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 2. R² Score Progress
    ax2 = plt.subplot(4, 3, 2)
    plt.plot(epochs, val_r2_scores, 'g-', label='Validation R²', alpha=0.8)
    plt.axhline(y=0.7, color='orange', linestyle='--', alpha=0.7, label='Target (0.7)')
    plt.xlabel('Epoch')
    plt.ylabel('R² Score')
    plt.title('R² Score Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 3. RMSE Progress
    ax3 = plt.subplot(4, 3, 3)
    plt.plot(epochs, val_rmse_scores, 'purple', label='Validation RMSE', alpha=0.8)
    plt.xlabel('Epoch')
    plt.ylabel('RMSE (ms)')
    plt.title('RMSE Progress')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 4. Predictions vs Actual (Log Scale)
    ax4 = plt.subplot(4, 3, 4)
    plt.scatter(y_val_orig, val_pred_orig, alpha=0.6, s=30)

    # Perfect prediction line
    min_val = min(y_val_orig.min(), val_pred_orig.min())
    max_val = max(y_val_orig.max(), val_pred_orig.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='Perfect Prediction')

    plt.xlabel('Actual Query Time (ms)')
    plt.ylabel('Predicted Query Time (ms)')
    plt.title(f'Predictions vs Actual\\nR² = {final_metrics["r2"]:.4f}')
    plt.xscale('log')
    plt.yscale('log')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 5. Residuals Plot
    ax5 = plt.subplot(4, 3, 5)
    residuals = y_val_orig - val_pred_orig
    plt.scatter(val_pred_orig, residuals, alpha=0.6, s=30)
    plt.axhline(y=0, color='r', linestyle='--', alpha=0.8)
    plt.xlabel('Predicted Query Time (ms)')
    plt.ylabel('Residuals (ms)')
    plt.title('Residuals vs Predicted')
    plt.xscale('log')
    plt.grid(True, alpha=0.3)

    # 6. Error Distribution
    ax6 = plt.subplot(4, 3, 6)
    plt.hist(residuals, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.axvline(residuals.mean(), color='red', linestyle='--',
                label=f'Mean: {residuals.mean():.1f} ms')
    plt.xlabel('Residuals (ms)')
    plt.ylabel('Frequency')
    plt.title('Error Distribution')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 7. Performance Metrics Summary
    ax7 = plt.subplot(4, 3, 7)
    metrics = ['R²', 'RMSE\\n(×1000)', 'MAE\\n(×1000)', 'MAPE\\n(%/10)', 'RAE\\n(×10)']
    values = [final_metrics['r2'], final_metrics['rmse']/1000, final_metrics['mae']/1000,
              final_metrics['mape']/10, final_metrics['rae']*10]
    colors = ['green', 'blue', 'orange', 'red', 'purple']

    bars = plt.bar(metrics, values, color=colors, alpha=0.7)
    plt.title('Performance Metrics Summary')
    plt.ylabel('Normalized Values')

    # Add value labels on bars
    for bar, value in zip(bars, values):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.3f}', ha='center', va='bottom')

    plt.grid(True, alpha=0.3)

    # 8. Learning Curves Comparison
    ax8 = plt.subplot(4, 3, 8)
    plt.plot(epochs, train_losses, 'b-', label='Training Loss', alpha=0.8)
    plt.plot(epochs, val_losses, 'r-', label='Validation Loss', alpha=0.8)
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Learning Curves')
    plt.legend()
    plt.yscale('log')
    plt.grid(True, alpha=0.3)

    # 9. Error by Query Size
    ax9 = plt.subplot(4, 3, 9)

    # Define size categories
    size_ranges = [(0, 1000), (1000, 5000), (5000, 20000), (20000, 50000), (50000, float('inf'))]
    size_labels = ['Very Small\\n(<1K)', 'Small\\n(1-5K)', 'Medium\\n(5-20K)', 'Large\\n(20-50K)', 'Very Large\\n(>50K)']

    category_errors = []
    category_counts = []

    for min_val, max_val in size_ranges:
        mask = (y_val_orig >= min_val) & (y_val_orig < max_val)
        if mask.sum() > 0:
            cat_mae = np.mean(np.abs(residuals[mask]))
            category_errors.append(cat_mae)
            category_counts.append(mask.sum())
        else:
            category_errors.append(0)
            category_counts.append(0)

    bars = plt.bar(size_labels, category_errors, alpha=0.7, color='lightcoral')
    plt.ylabel('Mean Absolute Error (ms)')
    plt.title('Error by Query Size Category')
    plt.xticks(rotation=45)

    # Add count labels
    for bar, count in zip(bars, category_counts):
        if count > 0:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 50,
                    f'n={count}', ha='center', va='bottom', fontsize=8)

    plt.grid(True, alpha=0.3)

    # 10. Training Stability
    ax10 = plt.subplot(4, 3, 10)

    # Calculate training volatility
    if len(train_losses) > 10:
        train_volatility = np.std(np.diff(train_losses[-50:]))  # Last 50 epochs
        val_volatility = np.std(np.diff(val_losses[-50:]))
    else:
        train_volatility = 0
        val_volatility = 0

    volatility_data = [train_volatility, val_volatility]
    volatility_labels = ['Training', 'Validation']
    colors = ['blue', 'red']

    bars = plt.bar(volatility_labels, volatility_data, color=colors, alpha=0.7)
    plt.ylabel('Loss Volatility (Std of Differences)')
    plt.title('Training Stability\\n(Lower is Better)')

    for bar, value in zip(bars, volatility_data):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value:.4f}', ha='center', va='bottom')

    plt.grid(True, alpha=0.3)

    # 11. Advanced Metrics Visualization
    ax11 = plt.subplot(4, 3, 11)

    advanced_metrics = ['RAE', 'RRSE', 'NRMSE', 'Pearson', 'Spearman']
    advanced_values = [final_metrics['rae'], final_metrics['rrse'], final_metrics['nrmse'],
                      final_metrics['pearson_corr'], final_metrics['spearman_corr']]

    # Normalize values for better visualization
    normalized_values = []
    for i, (metric, value) in enumerate(zip(advanced_metrics, advanced_values)):
        if metric in ['RAE', 'RRSE', 'NRMSE']:
            # For error metrics, lower is better, so invert
            normalized_values.append(max(0, 1 - value))
        else:
            # For correlation metrics, higher is better
            normalized_values.append(max(0, value))

    colors = ['orange', 'red', 'purple', 'green', 'blue']
    bars = plt.bar(advanced_metrics, normalized_values, color=colors, alpha=0.7)
    plt.ylabel('Normalized Score (Higher is Better)')
    plt.title('Advanced Metrics Comparison')
    plt.ylim(0, 1)

    for bar, orig_value in zip(bars, advanced_values):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{orig_value:.3f}', ha='center', va='bottom', fontsize=8)

    plt.grid(True, alpha=0.3)

    # 12. Model Quality Assessment
    ax12 = plt.subplot(4, 3, 12)

    # Quality indicators
    quality_metrics = {
        'R² Score': final_metrics['r2'],
        'Low MAPE': max(0, 1 - final_metrics['mape']/100),
        'High Correlation': final_metrics['pearson_corr'],
        'Low RAE': max(0, 1 - final_metrics['rae']),
        'Low RRSE': max(0, 1 - final_metrics['rrse'])
    }

    # Create radar-like visualization
    metrics_names = list(quality_metrics.keys())
    metrics_values = list(quality_metrics.values())

    # Color code based on quality
    colors = ['green' if v > 0.7 else 'orange' if v > 0.5 else 'red' for v in metrics_values]

    bars = plt.barh(metrics_names, metrics_values, color=colors, alpha=0.7)
    plt.xlabel('Quality Score (0-1)')
    plt.title('Model Quality Assessment')
    plt.xlim(0, 1)

    # Add value labels
    for bar, value in zip(bars, metrics_values):
        width = bar.get_width()
        plt.text(width + 0.02, bar.get_y() + bar.get_height()/2.,
                f'{value:.3f}', ha='left', va='center')

    # Add quality threshold lines
    plt.axvline(x=0.7, color='green', linestyle='--', alpha=0.5, label='Excellent (0.7+)')
    plt.axvline(x=0.5, color='orange', linestyle='--', alpha=0.5, label='Good (0.5+)')
    plt.legend(fontsize=8)
    plt.grid(True, alpha=0.3)

    # Adjust layout and save
    plt.tight_layout()

    # Save the comprehensive visualization
    filename = f'specific_features_training_results_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()

    print(f"Comprehensive visualization saved as: {filename}")

    return filename


def train_advanced_model(X_train, X_val, y_train, y_val, y_train_orig, y_val_orig,
                        preprocessor, epochs=1000, batch_size=64, learning_rate=0.001):
    """Train the advanced neural network model"""

    print("\\nStarting advanced model training...")
    print(f"Training samples: {len(X_train)}")
    print(f"Validation samples: {len(X_val)}")
    print(f"Features: {X_train.shape[1]}")

    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Create model
    model = AdvancedQueryPredictor(
        input_size=X_train.shape[1],
        hidden_sizes=[128, 64, 32],
        dropout_rate=0.3
    ).to(device)

    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

    # Loss and optimizer
    criterion = nn.MSELoss()
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=100, factor=0.5)

    # Data loaders
    train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train))
    val_dataset = TensorDataset(torch.FloatTensor(X_val), torch.FloatTensor(y_val))

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)

    # Training history
    train_losses = []
    val_losses = []
    val_r2_scores = []
    val_rmse_scores = []

    best_val_loss = float('inf')
    patience_counter = 0
    patience = 150

    print(f"\\nTraining for up to {epochs} epochs...")
    print("Epoch | Train Loss | Val Loss | Val R² | Val RMSE | LR")
    print("-" * 60)

    for epoch in range(epochs):
        # Training phase
        model.train()
        train_loss = 0.0

        for batch_X, batch_y in train_loader:
            batch_X, batch_y = batch_X.to(device), batch_y.to(device)

            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs.squeeze(), batch_y)
            loss.backward()

            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)

            optimizer.step()
            train_loss += loss.item()

        train_loss /= len(train_loader)

        # Validation phase
        model.eval()
        val_loss = 0.0
        val_predictions = []
        val_targets = []

        with torch.no_grad():
            for batch_X, batch_y in val_loader:
                batch_X, batch_y = batch_X.to(device), batch_y.to(device)
                outputs = model(batch_X)
                loss = criterion(outputs.squeeze(), batch_y)
                val_loss += loss.item()

                val_predictions.extend(outputs.squeeze().cpu().numpy())
                val_targets.extend(batch_y.cpu().numpy())

        val_loss /= len(val_loader)

        # Calculate R² and RMSE on original scale
        val_pred_log = preprocessor.target_scaler.inverse_transform(np.array(val_predictions).reshape(-1, 1))
        val_pred_orig = np.expm1(val_pred_log.flatten())
        val_pred_orig = np.maximum(val_pred_orig, 0)

        val_r2 = r2_score(y_val_orig, val_pred_orig)
        val_rmse = np.sqrt(mean_squared_error(y_val_orig, val_pred_orig))

        # Update learning rate
        scheduler.step(val_loss)

        # Track metrics
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        val_r2_scores.append(val_r2)
        val_rmse_scores.append(val_rmse)

        # Early stopping
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'best_specific_features_model.pth')
        else:
            patience_counter += 1

        # Print progress
        if epoch % 50 == 0 or epoch < 10:
            current_lr = optimizer.param_groups[0]['lr']
            print(f"{epoch:5d} | {train_loss:10.6f} | {val_loss:8.6f} | {val_r2:6.4f} | {val_rmse:8.2f} | {current_lr:.2e}")

        # Early stopping
        if patience_counter >= patience:
            print(f"\\nEarly stopping at epoch {epoch} (patience: {patience})")
            break

    # Load best model
    model.load_state_dict(torch.load('best_specific_features_model.pth'))

    print(f"\\nTraining completed!")
    print(f"Best validation loss: {best_val_loss:.6f}")
    print(f"Total epochs: {len(train_losses)}")

    return model, train_losses, val_losses, val_r2_scores, val_rmse_scores


def main():
    """Main execution function"""

    print("="*80)
    print("NEURAL NETWORK MODEL WITH SPECIFIC 10 FEATURES")
    print("="*80)
    print("Features used (exactly as requested from train2.csv):")
    specific_features = [
        '1. EstimatedTotalSubtreeCostHashMatch',
        '2. EstimateRowsHashMatch',
        '3. total_num_joins',
        '4. ClusteredIndexScanOpCount',
        '5. ClusteredIndexSeekOpCount',
        '6. SortOpCount',
        '7. total_estimated_cpu_cost',
        '8. total_estimated_io_cost',
        '9. EstimateRowsSort',
        '10. HashMatchOpCount'
    ]
    for feature in specific_features:
        print(f"  {feature}")
    print("="*80)

    # Load data
    print("\\nLoading data...")
    train_df = pd.read_csv("Dataset/Dataset/train/train2.csv")
    test_df = pd.read_csv("Dataset/Dataset/test/test2.csv")

    print(f"Training data shape: {train_df.shape}")
    print(f"Test data shape: {test_df.shape}")

    # Initialize feature extractor
    feature_extractor = AdvancedFeatureExtractor()

    # Extract features and target
    print("\\nExtracting features...")
    X, y = feature_extractor.fit_transform(train_df)
    X_test = feature_extractor.transform(test_df)

    print(f"Feature matrix shape: {X.shape}")
    print(f"Test matrix shape: {X_test.shape}")

    # Train-validation split with indices
    X_train, X_val, y_train, y_val, train_idx, val_idx = train_test_split(
        X, y, list(range(len(y))), test_size=0.2, random_state=42
    )

    y_train_orig = train_df.iloc[train_idx]['QueryTime']
    y_val_orig = train_df.iloc[val_idx]['QueryTime']

    print(f"Training samples: {len(X_train)}")
    print(f"Validation samples: {len(X_val)}")

    # Initialize preprocessor
    preprocessor = DataPreprocessor()

    # Preprocess features
    X_train_scaled = preprocessor.fit_transform_features(X_train)
    X_val_scaled = preprocessor.transform_features(X_val)
    X_test_scaled = preprocessor.transform_features(X_test)

    # Preprocess target
    y_train_scaled = preprocessor.fit_transform_target(y_train_orig)
    y_val_scaled = preprocessor.transform_target(y_val_orig)

    print(f"\\nData preprocessing completed")
    print(f"Scaled training features shape: {X_train_scaled.shape}")
    print(f"Scaled target range: [{y_train_scaled.min():.3f}, {y_train_scaled.max():.3f}]")

    # Train model
    model, train_losses, val_losses, val_r2_scores, val_rmse_scores = train_advanced_model(
        X_train_scaled, X_val_scaled, y_train_scaled, y_val_scaled,
        y_train_orig, y_val_orig, preprocessor,
        epochs=1000, batch_size=64, learning_rate=0.001
    )

    # Final evaluation
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    final_metrics, val_pred_orig = evaluate_model_performance(
        model, X_val_scaled, y_val_orig, preprocessor, device
    )

    print_metrics(final_metrics, "Final Model Performance")

    # Cross-validation
    cv_metrics = perform_cross_validation(
        X_train_scaled, y_train_scaled, y_train_orig, preprocessor,
        cv_folds=5, cv_epochs=200
    )

    # Generate test predictions
    test_predictions = generate_test_predictions(model, X_test_scaled, preprocessor, device)

    # Create visualizations
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    visualization_file = create_comprehensive_visualizations(
        train_losses, val_losses, val_r2_scores, val_rmse_scores,
        y_val_orig, val_pred_orig, final_metrics, timestamp
    )

    # Save results
    results = {
        'model_config': {
            'features_used': feature_extractor.selected_features,
            'num_features': len(feature_extractor.selected_features),
            'architecture': [128, 64, 32],
            'total_parameters': sum(p.numel() for p in model.parameters())
        },
        'training_history': {
            'epochs_trained': len(train_losses),
            'best_val_loss': min(val_losses),
            'final_train_loss': train_losses[-1],
            'final_val_loss': val_losses[-1]
        },
        'performance_metrics': final_metrics,
        'cross_validation': {
            'cv_r2_mean': cv_metrics['r2'],
            'cv_r2_std': cv_metrics['r2_std'],
            'cv_rmse_mean': cv_metrics['rmse'],
            'cv_rmse_std': cv_metrics['rmse_std']
        },
        'test_predictions': test_predictions['mean'].tolist()
    }

    # Save to JSON
    results_file = f'specific_features_results_{timestamp}.json'
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)

    # Save model
    model_file = f'specific_features_model_{timestamp}.pth'
    torch.save({
        'model_state_dict': model.state_dict(),
        'model_config': results['model_config'],
        'preprocessor_state': {
            'feature_scaler': preprocessor.feature_scaler,
            'target_scaler': preprocessor.target_scaler
        },
        'feature_names': feature_extractor.selected_features
    }, model_file)

    print(f"\\n" + "="*80)
    print("TRAINING COMPLETED SUCCESSFULLY!")
    print("="*80)
    print(f"Results saved to: {results_file}")
    print(f"Model saved to: {model_file}")
    print(f"Visualization saved to: {visualization_file}")
    print(f"\\nFinal Performance Summary:")
    print(f"  R² Score: {final_metrics['r2']:.4f}")
    print(f"  RMSE: {final_metrics['rmse']:,.2f} ms")
    print(f"  MAE: {final_metrics['mae']:,.2f} ms")
    print(f"  MAPE: {final_metrics['mape']:.2f}%")
    print(f"  Features used: {len(feature_extractor.selected_features)}")
    print("="*80)

    return model, results, final_metrics


if __name__ == "__main__":
    model, results, metrics = main()
