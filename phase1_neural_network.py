#!/usr/bin/env python3
"""
Phase 1 Neural Network Implementation - Top 10 Essential Features
Query Execution Time Prediction using the most predictive features identified from comprehensive analysis
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split, KFold
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import json
import warnings
warnings.filterwarnings('ignore')

class Phase1QueryPredictor(nn.Module):
    """Optimized neural network for Phase 1 - Top 10 features"""
    
    def __init__(self, input_size=10, hidden_sizes=[64, 32, 16], dropout_rate=0.3):
        super(Phase1QueryPredictor, self).__init__()
        
        self.input_size = input_size
        self.hidden_sizes = hidden_sizes
        
        # Input layer
        self.input_layer = nn.Sequential(
            nn.Linear(input_size, hidden_sizes[0]),
            nn.BatchNorm1d(hidden_sizes[0]),
            nn.ReLU(),
            nn.Dropout(dropout_rate)
        )
        
        # Hidden layers
        self.hidden_layers = nn.ModuleList()
        for i in range(len(hidden_sizes) - 1):
            layer = nn.Sequential(
                nn.Linear(hidden_sizes[i], hidden_sizes[i + 1]),
                nn.BatchNorm1d(hidden_sizes[i + 1]),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            )
            self.hidden_layers.append(layer)
        
        # Output layer
        self.output_layer = nn.Linear(hidden_sizes[-1], 1)
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Xavier/Glorot initialization for better gradient flow"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                nn.init.zeros_(module.bias)
    
    def forward(self, x):
        x = self.input_layer(x)
        
        for layer in self.hidden_layers:
            residual = x
            x = layer(x)
            # Add residual connection if dimensions match
            if x.shape == residual.shape:
                x = x + residual
        
        return self.output_layer(x)

class Phase1DataProcessor:
    """Data preprocessing for Phase 1 implementation"""
    
    def __init__(self):
        # Top 10 essential features from comprehensive analysis
        self.essential_features = [
            'EstimatedTotalSubtreeCostComputeScalar',
            'EstimateRowsHashMatch', 
            'EstimatedTotalSubtreeCostNestedLoops',
            'EstimateCPUStreamAggregate',
            'EstimatedTotalSubtreeCostClusteredIndexSeek',
            'EstimatedTotalSubtreeCostStreamAggregate',
            'EstimateCPUClusteredIndexScan',
            'EstimateRowsClusteredIndexScan',
            'EstimatedTotalSubtreeCostSort',
            'EstimateCPUNestedLoops'
        ]
        
        self.feature_scaler = None
        self.target_scaler = None
        
    def load_and_preprocess(self, train_path, test_path):
        """Load and preprocess data with Phase 1 features"""
        print("="*60)
        print("PHASE 1 DATA PREPROCESSING")
        print("="*60)
        
        # Load data
        train_df = pd.read_csv(train_path)
        test_df = pd.read_csv(test_path)
        
        print(f"Original training data: {train_df.shape}")
        print(f"Original test data: {test_df.shape}")
        
        # Extract Phase 1 features
        print(f"\nUsing Phase 1 essential features ({len(self.essential_features)}):")
        for i, feature in enumerate(self.essential_features, 1):
            print(f"  {i:2d}. {feature}")
        
        # Check feature availability
        missing_features = [f for f in self.essential_features if f not in train_df.columns]
        if missing_features:
            print(f"\nWarning: Missing features: {missing_features}")
            self.essential_features = [f for f in self.essential_features if f in train_df.columns]
            print(f"Using {len(self.essential_features)} available features")
        
        # Extract features and target
        X_train_full = train_df[self.essential_features].fillna(0)
        y_train_full = train_df['QueryTime']
        X_test = test_df[self.essential_features].fillna(0)
        
        # Target analysis
        print(f"\nTarget Variable Analysis:")
        print(f"Range: {y_train_full.min():.2f} to {y_train_full.max():,.2f} ms")
        print(f"Mean: {y_train_full.mean():,.2f} ms")
        print(f"Median: {y_train_full.median():,.2f} ms")
        print(f"Skewness: {y_train_full.skew():.3f}")
        
        # Log transform target (essential for this highly skewed data)
        y_train_log = np.log1p(y_train_full)
        print(f"After log transform - Range: {y_train_log.min():.3f} to {y_train_log.max():.3f}")
        print(f"After log transform - Skewness: {y_train_log.skew():.3f}")
        
        # Train-validation split
        X_train, X_val, y_train, y_val = train_test_split(
            X_train_full, y_train_log, test_size=0.2, random_state=42
        )
        
        # Keep original values for evaluation
        y_train_orig = train_df.loc[X_train.index, 'QueryTime']
        y_val_orig = train_df.loc[X_val.index, 'QueryTime']
        
        # Feature scaling (RobustScaler for outlier resistance)
        print(f"\nApplying RobustScaler to features...")
        self.feature_scaler = RobustScaler()
        X_train_scaled = self.feature_scaler.fit_transform(X_train)
        X_val_scaled = self.feature_scaler.transform(X_val)
        X_test_scaled = self.feature_scaler.transform(X_test)
        
        # Target scaling for neural network stability
        self.target_scaler = StandardScaler()
        y_train_scaled = self.target_scaler.fit_transform(y_train.values.reshape(-1, 1)).flatten()
        y_val_scaled = self.target_scaler.transform(y_val.values.reshape(-1, 1)).flatten()
        
        print(f"\nPreprocessed data shapes:")
        print(f"Training: X={X_train_scaled.shape}, y={y_train_scaled.shape}")
        print(f"Validation: X={X_val_scaled.shape}, y={y_val_scaled.shape}")
        print(f"Test: X={X_test_scaled.shape}")
        
        # Feature statistics
        print(f"\nFeature statistics after scaling:")
        print(f"Training features - Mean: {X_train_scaled.mean():.3f}, Std: {X_train_scaled.std():.3f}")
        print(f"Training target - Mean: {y_train_scaled.mean():.3f}, Std: {y_train_scaled.std():.3f}")
        
        return (X_train_scaled, X_val_scaled, X_test_scaled, 
                y_train_scaled, y_val_scaled,
                y_train_orig, y_val_orig, y_train_full)

def train_phase1_model(X_train, X_val, y_train, y_val, y_train_orig, y_val_orig, 
                      epochs=500, batch_size=32, learning_rate=0.001):
    """Train Phase 1 neural network model"""
    
    print("\n" + "="*60)
    print("PHASE 1 MODEL TRAINING")
    print("="*60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create model
    model = Phase1QueryPredictor(
        input_size=X_train.shape[1],
        hidden_sizes=[64, 32, 16],
        dropout_rate=0.3
    ).to(device)
    
    print(f"\nModel Architecture:")
    print(f"Input size: {X_train.shape[1]} (Phase 1 essential features)")
    print(f"Hidden layers: [64, 32, 16]")
    print(f"Total parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Loss and optimizer
    criterion = nn.MSELoss()
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=20, factor=0.5)
    
    # Data loaders
    train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train))
    val_dataset = TensorDataset(torch.FloatTensor(X_val), torch.FloatTensor(y_val))
    
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    # Training history
    train_losses = []
    val_losses = []
    val_r2_scores = []
    
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 50
    
    print(f"\nStarting training for {epochs} epochs...")
    print("Epoch | Train Loss | Val Loss | Val R² | LR | Status")
    print("-" * 55)
    
    for epoch in range(epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        
        for batch_X, batch_y in train_loader:
            batch_X, batch_y = batch_X.to(device), batch_y.to(device)
            
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs.squeeze(), batch_y)
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_predictions = []
        val_targets = []
        
        with torch.no_grad():
            for batch_X, batch_y in val_loader:
                batch_X, batch_y = batch_X.to(device), batch_y.to(device)
                outputs = model(batch_X)
                loss = criterion(outputs.squeeze(), batch_y)
                val_loss += loss.item()
                
                val_predictions.extend(outputs.squeeze().cpu().numpy())
                val_targets.extend(batch_y.cpu().numpy())
        
        val_loss /= len(val_loader)
        
        # Calculate R² on original scale
        val_pred_orig = inverse_transform_predictions(val_predictions, processor.target_scaler)
        val_r2 = r2_score(y_val_orig, val_pred_orig)
        
        # Update learning rate
        scheduler.step(val_loss)
        
        # Track metrics
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        val_r2_scores.append(val_r2)
        
        # Early stopping
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            torch.save(model.state_dict(), 'phase1_best_model.pth')
            status = "✓"
        else:
            patience_counter += 1
            status = ""
        
        # Print progress
        if epoch % 10 == 0 or epoch < 10:
            current_lr = optimizer.param_groups[0]['lr']
            print(f"{epoch:5d} | {train_loss:10.6f} | {val_loss:8.6f} | {val_r2:6.4f} | {current_lr:.2e} | {status}")
        
        # Early stopping
        if patience_counter >= patience:
            print(f"\nEarly stopping at epoch {epoch} (patience: {patience})")
            break
    
    # Load best model
    model.load_state_dict(torch.load('phase1_best_model.pth'))
    
    print(f"\nTraining completed!")
    print(f"Best validation loss: {best_val_loss:.6f}")
    
    return model, train_losses, val_losses, val_r2_scores

def inverse_transform_predictions(predictions, target_scaler):
    """Convert scaled predictions back to original scale"""
    # Inverse scale
    pred_log = target_scaler.inverse_transform(np.array(predictions).reshape(-1, 1)).flatten()
    # Inverse log transform
    pred_orig = np.expm1(pred_log)
    # Ensure non-negative
    pred_orig = np.maximum(pred_orig, 0)
    return pred_orig

def evaluate_phase1_model(model, X_val, y_val_orig, processor, device):
    """Comprehensive evaluation of Phase 1 model"""
    
    print("\n" + "="*60)
    print("PHASE 1 MODEL EVALUATION")
    print("="*60)
    
    model.eval()
    with torch.no_grad():
        X_val_tensor = torch.FloatTensor(X_val).to(device)
        val_pred_scaled = model(X_val_tensor).cpu().numpy().flatten()
    
    # Convert to original scale
    val_pred_orig = inverse_transform_predictions(val_pred_scaled, processor.target_scaler)
    
    # Calculate metrics
    rmse = np.sqrt(mean_squared_error(y_val_orig, val_pred_orig))
    mae = mean_absolute_error(y_val_orig, val_pred_orig)
    r2 = r2_score(y_val_orig, val_pred_orig)
    mape = np.mean(np.abs((y_val_orig - val_pred_orig) / y_val_orig)) * 100
    
    print(f"Phase 1 Model Performance:")
    print(f"R² Score: {r2:.4f}")
    print(f"RMSE: {rmse:,.2f} ms")
    print(f"MAE: {mae:,.2f} ms") 
    print(f"MAPE: {mape:.2f}%")
    
    # Additional metrics
    median_ae = np.median(np.abs(y_val_orig - val_pred_orig))
    print(f"Median AE: {median_ae:,.2f} ms")
    
    # Prediction quality analysis
    residuals = y_val_orig - val_pred_orig
    print(f"\nPrediction Analysis:")
    print(f"Mean residual: {residuals.mean():,.2f} ms")
    print(f"Std residual: {residuals.std():,.2f} ms")
    print(f"Negative predictions: {(val_pred_orig < 0).sum()}")
    print(f"Prediction range: {val_pred_orig.min():,.2f} to {val_pred_orig.max():,.2f} ms")
    print(f"Actual range: {y_val_orig.min():,.2f} to {y_val_orig.max():,.2f} ms")
    
    return {
        'r2': r2,
        'rmse': rmse,
        'mae': mae,
        'mape': mape,
        'median_ae': median_ae,
        'predictions': val_pred_orig,
        'residuals': residuals
    }

def main():
    """Main Phase 1 implementation"""
    
    print("="*80)
    print("PHASE 1: NEURAL NETWORK WITH TOP 10 ESSENTIAL FEATURES")
    print("="*80)
    
    # Initialize processor
    global processor
    processor = Phase1DataProcessor()
    
    # Load and preprocess data
    data = processor.load_and_preprocess(
        "Dataset/Dataset/train/train.csv",
        "Dataset/Dataset/test/test.csv"
    )
    
    X_train, X_val, X_test, y_train, y_val, y_train_orig, y_val_orig, y_train_full = data
    
    # Train model
    model, train_losses, val_losses, val_r2_scores = train_phase1_model(
        X_train, X_val, y_train, y_val, y_train_orig, y_val_orig,
        epochs=500, batch_size=32, learning_rate=0.001
    )
    
    # Evaluate model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    results = evaluate_phase1_model(model, X_val, y_val_orig, processor, device)
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Save model
    torch.save({
        'model_state_dict': model.state_dict(),
        'model_config': {
            'input_size': len(processor.essential_features),
            'hidden_sizes': [64, 32, 16],
            'features': processor.essential_features
        },
        'results': results,
        'training_history': {
            'train_losses': train_losses,
            'val_losses': val_losses,
            'val_r2_scores': val_r2_scores
        }
    }, f'phase1_model_{timestamp}.pth')
    
    # Save metrics
    with open(f'phase1_metrics_{timestamp}.json', 'w') as f:
        json.dump({
            'phase': 1,
            'features_used': len(processor.essential_features),
            'feature_list': processor.essential_features,
            'performance': {k: float(v) if isinstance(v, (int, float, np.number)) else str(v) 
                          for k, v in results.items() if k not in ['predictions', 'residuals']}
        }, f, indent=2)
    
    print(f"\n" + "="*60)
    print("PHASE 1 IMPLEMENTATION COMPLETED SUCCESSFULLY!")
    print("="*60)
    print(f"Model saved: phase1_model_{timestamp}.pth")
    print(f"Metrics saved: phase1_metrics_{timestamp}.json")
    print(f"\nPhase 1 Results Summary:")
    print(f"- Features used: {len(processor.essential_features)} (top essential features)")
    print(f"- R² Score: {results['r2']:.4f}")
    print(f"- RMSE: {results['rmse']:,.2f} ms")
    print(f"- Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    return model, processor, results

if __name__ == "__main__":
    model, processor, results = main()
