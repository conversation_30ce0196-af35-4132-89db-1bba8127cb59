"""
Query Execution Time Prediction: Complete Preprocessing and Feature Engineering Pipeline
=======================================================================================

This script implements a comprehensive preprocessing and feature engineering pipeline
for predicting query execution times based on database query execution plan statistics.
"""

import numpy as np
import pandas as pd
from sklearn.preprocessing import (
    StandardScaler, RobustScaler, MinMaxScaler, PowerTransformer, 
    QuantileTransformer, FunctionTransformer
)
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.pipeline import Pipeline, FeatureUnion
from sklearn.compose import ColumnTransformer
from sklearn.base import BaseEstimator, TransformerMixin
from sklearn.feature_selection import SelectKBest, mutual_info_regression
from scipy import stats
from scipy.special import boxcox1p
import warnings
warnings.filterwarnings('ignore')


class OutlierHandler(BaseEstimator, TransformerMixin):
    """Custom transformer for handling outliers using winsorization or capping"""
    
    def __init__(self, method='winsorize', percentile=99):
        self.method = method
        self.percentile = percentile
        self.bounds_ = {}
    
    def fit(self, X, y=None):
        X_df = pd.DataFrame(X)
        for col in X_df.columns:
            if self.method == 'winsorize':
                lower = np.percentile(X_df[col], 100 - self.percentile)
                upper = np.percentile(X_df[col], self.percentile)
            elif self.method == 'iqr':
                Q1 = X_df[col].quantile(0.25)
                Q3 = X_df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower = Q1 - 1.5 * IQR
                upper = Q3 + 1.5 * IQR
            self.bounds_[col] = (lower, upper)
        return self
    
    def transform(self, X):
        X_df = pd.DataFrame(X)
        X_transformed = X_df.copy()
        for col in X_df.columns:
            lower, upper = self.bounds_[col]
            X_transformed[col] = X_transformed[col].clip(lower, upper)
        return X_transformed.values


class FeatureEngineer(BaseEstimator, TransformerMixin):
    """Custom transformer for creating engineered features"""
    
    def __init__(self, create_ratios=True, create_logs=True, create_interactions=True):
        self.create_ratios = create_ratios
        self.create_logs = create_logs
        self.create_interactions = create_interactions
        self.feature_names_ = []
    
    def fit(self, X, y=None):
        return self
    
    def transform(self, X):
        # Convert to DataFrame for easier manipulation
        feature_names = [
            'ClusteredIndexScanOpCount', 'ClusteredIndexSeekOpCount', 
            'HashMatchOpCount', 'EstimateRowsHashMatch', 
            'EstimatedTotalSubtreeCostHashMatch', 'SortOpCount', 
            'EstimateRowsSort', 'total_num_joins', 
            'total_estimated_cpu_cost', 'total_estimated_io_cost'
        ]
        
        X_df = pd.DataFrame(X, columns=feature_names[:X.shape[1]])
        X_eng = X_df.copy()
        
        # Create ratio features
        if self.create_ratios:
            # Cost per join
            X_eng['CostPerJoin'] = np.where(
                X_df['total_num_joins'] > 0,
                (X_df['total_estimated_cpu_cost'] + X_df['total_estimated_io_cost']) / X_df['total_num_joins'],
                0
            )
            
            # IO to CPU ratio
            X_eng['IOtoCPURatio'] = np.where(
                X_df['total_estimated_cpu_cost'] > 0,
                X_df['total_estimated_io_cost'] / X_df['total_estimated_cpu_cost'],
                0
            )
            
            # Average rows per hash match
            X_eng['AvgRowsPerHashMatch'] = np.where(
                X_df['HashMatchOpCount'] > 0,
                X_df['EstimateRowsHashMatch'] / X_df['HashMatchOpCount'],
                0
            )
            
            # Average rows per sort
            X_eng['AvgRowsPerSort'] = np.where(
                X_df['SortOpCount'] > 0,
                X_df['EstimateRowsSort'] / X_df['SortOpCount'],
                0
            )
        
        # Create aggregation features
        X_eng['TotalOperations'] = (
            X_df['ClusteredIndexScanOpCount'] + 
            X_df['ClusteredIndexSeekOpCount'] + 
            X_df['HashMatchOpCount'] + 
            X_df['SortOpCount']
        )
        
        X_eng['TotalCost'] = X_df['total_estimated_cpu_cost'] + X_df['total_estimated_io_cost']
        
        # Complexity score
        X_eng['ComplexityScore'] = (
            X_df['HashMatchOpCount'] * X_df['SortOpCount'] * X_df['total_num_joins']
        )
        
        # Create log-transformed features for skewed variables
        if self.create_logs:
            skewed_features = [
                'EstimateRowsHashMatch', 'EstimateRowsSort', 
                'EstimatedTotalSubtreeCostHashMatch', 'TotalCost'
            ]
            for feat in skewed_features:
                if feat in X_eng.columns:
                    X_eng[f'Log_{feat}'] = np.log1p(X_eng[feat])
        
        # Create polynomial features
        X_eng['TotalCostSquared'] = X_eng['TotalCost'] ** 2
        X_eng['JoinsSquared'] = X_df['total_num_joins'] ** 2
        X_eng['OperationsSquared'] = X_eng['TotalOperations'] ** 2
        
        # Create interaction features
        if self.create_interactions:
            X_eng['ScanSeekInteraction'] = (
                X_df['ClusteredIndexScanOpCount'] * X_df['ClusteredIndexSeekOpCount']
            )
            X_eng['HashSortInteraction'] = (
                X_df['HashMatchOpCount'] * X_df['SortOpCount']
            )
            X_eng['CostJoinInteraction'] = (
                X_eng['TotalCost'] * X_df['total_num_joins']
            )
            X_eng['ComplexityIOInteraction'] = (
                X_eng['ComplexityScore'] * X_df['total_estimated_io_cost']
            )
        
        # Store feature names
        self.feature_names_ = X_eng.columns.tolist()
        
        return X_eng.values


class TargetTransformer:
    """Handles various transformations for the target variable"""
    
    def __init__(self, method='yeo-johnson'):
        self.method = method
        self.transformer = None
        self.lambda_ = None
    
    def fit_transform(self, y):
        if self.method == 'log':
            return np.log1p(y)
        
        elif self.method == 'sqrt':
            return np.sqrt(y)
        
        elif self.method == 'cbrt':
            return np.cbrt(y)
        
        elif self.method == 'yeo-johnson':
            # Use PowerTransformer with yeo-johnson method
            self.transformer = PowerTransformer(method='yeo-johnson')
            return self.transformer.fit_transform(y.reshape(-1, 1)).ravel()
        
        elif self.method == 'box-cox':
            # Find optimal lambda for Box-Cox
            y_positive = y + 1  # Ensure all values are positive
            transformed, self.lambda_ = stats.boxcox(y_positive)
            return transformed
        
        elif self.method == 'quantile':
            # Quantile transformation to normal distribution
            self.transformer = QuantileTransformer(output_distribution='normal')
            return self.transformer.fit_transform(y.reshape(-1, 1)).ravel()
        
        else:
            return y
    
    def inverse_transform(self, y_transformed):
        if self.method == 'log':
            return np.expm1(y_transformed)
        
        elif self.method == 'sqrt':
            return y_transformed ** 2
        
        elif self.method == 'cbrt':
            return y_transformed ** 3
        
        elif self.method == 'yeo-johnson':
            return self.transformer.inverse_transform(y_transformed.reshape(-1, 1)).ravel()
        
        elif self.method == 'box-cox':
            # Inverse Box-Cox transformation
            return stats.inv_boxcox(y_transformed, self.lambda_) - 1
        
        elif self.method == 'quantile':
            return self.transformer.inverse_transform(y_transformed.reshape(-1, 1)).ravel()
        
        else:
            return y_transformed


def create_preprocessing_pipeline():
    """Creates the complete preprocessing pipeline"""
    
    # Define column groups based on their characteristics
    sparse_cols = [0, 1, 5]  # ClusteredIndexSeekOpCount, HashMatchOpCount, SortOpCount
    skewed_cols = [3, 4, 6]  # EstimateRowsHashMatch, EstimatedTotalSubtreeCostHashMatch, EstimateRowsSort
    normal_cols = [2, 7]     # ClusteredIndexScanOpCount, total_num_joins
    cost_cols = [8, 9]       # total_estimated_cpu_cost, total_estimated_io_cost
    
    # Create preprocessing for different column types
    preprocessor = ColumnTransformer([
        # Handle sparse features with standard scaling
        ('sparse', StandardScaler(), sparse_cols),
        
        # Log transform + robust scaling for highly skewed features
        ('skewed', Pipeline([
            ('log', FunctionTransformer(np.log1p, validate=True)),
            ('scale', RobustScaler())
        ]), skewed_cols),
        
        # Standard scaling for normally distributed features
        ('normal', StandardScaler(), normal_cols),
        
        # Robust scaling for cost features (may have outliers)
        ('cost', RobustScaler(), cost_cols)
    ])
    
    # Complete pipeline
    pipeline = Pipeline([
        # Step 1: Outlier handling
        ('outlier_handler', OutlierHandler(method='winsorize', percentile=99)),
        
        # Step 2: Feature engineering
        ('feature_engineer', FeatureEngineer(
            create_ratios=True,
            create_logs=True,
            create_interactions=True
        )),
        
        # Step 3: Feature scaling (applied after engineering)
        # Note: In practice, you'd need to update column indices after feature engineering
        ('scaler', StandardScaler())  # Simple scaling for all engineered features
    ])
    
    return pipeline


def evaluate_target_transformations(y):
    """Evaluates different target transformations and returns statistics"""
    
    transformations = {
        'Original': y,
        'Log': np.log1p(y),
        'Square Root': np.sqrt(y),
        'Cube Root': np.cbrt(y),
        'Yeo-Johnson': PowerTransformer(method='yeo-johnson').fit_transform(y.reshape(-1, 1)).ravel(),
        'Box-Cox (λ=0.25)': boxcox1p(y, 0.25),
        'Quantile Normal': QuantileTransformer(output_distribution='normal').fit_transform(y.reshape(-1, 1)).ravel()
    }
    
    results = []
    for name, transformed in transformations.items():
        # Calculate statistics
        skewness = stats.skew(transformed)
        kurtosis = stats.kurtosis(transformed)
        cv = np.std(transformed) / np.mean(transformed) if np.mean(transformed) != 0 else np.inf
        
        results.append({
            'Transformation': name,
            'Skewness': skewness,
            'Kurtosis': kurtosis,
            'CV': cv,
            'Mean': np.mean(transformed),
            'Median': np.median(transformed),
            'Std': np.std(transformed)
        })
    
    return pd.DataFrame(results)


def create_stratified_splits(X, y, n_splits=5):
    """Creates stratified splits based on binned target values"""
    
    # Bin the target variable for stratification
    y_binned = pd.qcut(y, q=n_splits, labels=False, duplicates='drop')
    
    # Create stratified k-fold
    skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)
    
    return skf, y_binned


# Example usage
if __name__ == "__main__":
    # Load your data
    train_df = pd.read_csv('Dataset/Dataset/train/train2.csv')
    test_df = pd.read_csv('Dataset/Dataset/test/test2.csv')

    # Extract features and target
    feature_columns = [
        'ClusteredIndexScanOpCount', 'ClusteredIndexSeekOpCount',
        'HashMatchOpCount', 'EstimateRowsHashMatch',
        'EstimatedTotalSubtreeCostHashMatch', 'SortOpCount',
        'EstimateRowsSort', 'total_num_joins',
        'total_estimated_cpu_cost ', 'total_estimated_io_cost'
    ]

    X = train_df[feature_columns].values
    y = train_df['QueryTime'].values

    print(f"Loaded real data: X shape {X.shape}, y shape {y.shape}")
    print(f"Target statistics: mean={y.mean():.2f}, std={y.std():.2f}, min={y.min():.2f}, max={y.max():.2f}")

    # Also create synthetic data for comparison
    print("\n" + "="*60)
    print("CREATING SYNTHETIC DATA FOR COMPARISON")
    print("="*60)

    # Simulate synthetic data for comparison
    np.random.seed(42)
    n_samples = 1000

    # Create synthetic data similar to the problem
    X_synthetic = np.random.rand(n_samples, 10)
    X_synthetic[:, 1] = np.random.poisson(2, n_samples)  # Sparse feature
    X_synthetic[:, 3] = np.random.lognormal(10, 2, n_samples)  # Highly skewed

    # Create target with extreme skew
    y_synthetic = np.random.lognormal(9, 1.5, n_samples)
    
    # Evaluate target transformations on REAL data
    print("REAL DATA - Target Transformation Analysis:")
    print("=" * 60)
    transform_results_real = evaluate_target_transformations(y)
    print(transform_results_real.to_string(index=False))

    # Create preprocessing pipeline
    pipeline = create_preprocessing_pipeline()

    # Transform features (REAL data)
    X_transformed = pipeline.fit_transform(X)
    print(f"\nREAL DATA - Original shape: {X.shape}")
    print(f"REAL DATA - Transformed shape: {X_transformed.shape}")

    # Apply target transformation (REAL data)
    target_transformer = TargetTransformer(method='yeo-johnson')
    y_transformed = target_transformer.fit_transform(y)

    # Create stratified splits (REAL data)
    skf, y_binned = create_stratified_splits(X, y, n_splits=5)

    print(f"\nREAL DATA - Target bins distribution:")
    print(pd.Series(y_binned).value_counts().sort_index())

    # Feature importance analysis (using mutual information) on REAL data
    print("\nREAL DATA - Feature Importance (Mutual Information):")
    print("=" * 50)

    # Get feature names after engineering
    feature_engineer = FeatureEngineer()
    X_eng = feature_engineer.fit_transform(X)
    feature_names = feature_engineer.feature_names_

    # Calculate mutual information
    mi_scores = mutual_info_regression(X_eng, y_transformed)
    mi_df = pd.DataFrame({
        'Feature': feature_names,
        'MI Score': mi_scores
    }).sort_values('MI Score', ascending=False)

    print(mi_df.head(15).to_string(index=False))

    # Now process synthetic data for comparison
    print("\n" + "="*60)
    print("SYNTHETIC DATA - Target Transformation Analysis:")
    print("=" * 60)
    transform_results_synthetic = evaluate_target_transformations(y_synthetic)
    print(transform_results_synthetic.to_string(index=False))

    # Transform synthetic features
    X_synthetic_transformed = pipeline.fit_transform(X_synthetic)
    print(f"\nSYNTHETIC DATA - Original shape: {X_synthetic.shape}")
    print(f"SYNTHETIC DATA - Transformed shape: {X_synthetic_transformed.shape}")

    # Apply target transformation (SYNTHETIC data)
    target_transformer_synthetic = TargetTransformer(method='yeo-johnson')
    y_synthetic_transformed = target_transformer_synthetic.fit_transform(y_synthetic)

    print(f"\nComparison Summary:")
    print(f"Real data target range: {y.min():.2f} - {y.max():.2f}")
    print(f"Synthetic data target range: {y_synthetic.min():.2f} - {y_synthetic.max():.2f}")
    print(f"Real data skewness: {stats.skew(y):.3f}")
    print(f"Synthetic data skewness: {stats.skew(y_synthetic):.3f}")
