#!/usr/bin/env python3
"""
Verify that the metrics calculation fix works correctly
"""

import numpy as np
import torch
import torch.nn as nn
from sklearn.metrics import r2_score, mean_squared_error
from sklearn.preprocessing import StandardScaler, RobustScaler
import pandas as pd
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_train_eval_mode_consistency():
    """Test that training metrics are calculated consistently"""
    print("🔍 TESTING TRAIN/EVAL MODE CONSISTENCY FOR METRICS")
    print("=" * 60)
    
    # Load actual data
    try:
        train_df = pd.read_csv('Dataset/Dataset/train/train2.csv')
        print(f"✅ Loaded training data: {len(train_df)} samples")
    except Exception as e:
        print(f"❌ Could not load data: {e}")
        return False
    
    # Extract features and targets (simplified)
    feature_cols = [
        'EstimatedTotalSubtreeCostHashMatch',
        'EstimateRowsHashMatch', 
        'total_num_joins',
        'ClusteredIndexScanOpCount',
        'ClusteredIndexSeekOpCount',
        'SortOpCount',
        'total_estimated_cpu_cost',
        'total_estimated_io_cost',
        'EstimateRowsSort',
        'HashMatchOpCount'
    ]
    
    available_features = [f for f in feature_cols if f in train_df.columns]
    X_train = train_df[available_features].fillna(0).values[:100]  # Use first 100 samples
    y_train_orig = train_df['QueryTime'].values[:100]
    
    print(f"Using {len(available_features)} features, {len(X_train)} samples")
    
    # Preprocessing
    feature_scaler = RobustScaler()
    target_scaler = StandardScaler()
    
    X_train_scaled = feature_scaler.fit_transform(X_train)
    
    # Target preprocessing (same as main code)
    y_train_log = np.log1p(y_train_orig)
    y_train_scaled = target_scaler.fit_transform(y_train_log.reshape(-1, 1)).flatten()
    
    # Create model with dropout
    class TestModel(nn.Module):
        def __init__(self, input_size):
            super().__init__()
            self.layers = nn.Sequential(
                nn.Linear(input_size, 64),
                nn.Tanh(),
                nn.Dropout(0.1),  # This is the key - dropout affects predictions
                nn.Linear(64, 32),
                nn.Tanh(),
                nn.Dropout(0.1),
                nn.Linear(32, 1)
            )
        
        def forward(self, x):
            return self.layers(x)
    
    model = TestModel(X_train_scaled.shape[1])
    
    # Convert to tensors
    X_tensor = torch.FloatTensor(X_train_scaled)
    y_tensor = torch.FloatTensor(y_train_scaled)
    
    # Test 1: Compare predictions in train vs eval mode
    print("\n🔍 TEST 1: PREDICTION DIFFERENCES")
    print("-" * 40)
    
    # Predictions in train mode (with dropout)
    model.train()
    with torch.no_grad():
        pred_train_mode = model(X_tensor).squeeze().numpy()
    
    # Predictions in eval mode (without dropout)
    model.eval()
    with torch.no_grad():
        pred_eval_mode = model(X_tensor).squeeze().numpy()
    
    diff = np.abs(pred_train_mode - pred_eval_mode)
    max_diff = np.max(diff)
    mean_diff = np.mean(diff)
    
    print(f"Max difference: {max_diff:.6f}")
    print(f"Mean difference: {mean_diff:.6f}")
    print(f"Std difference: {np.std(diff):.6f}")
    
    if max_diff > 0.01:
        print("✅ Significant difference detected (expected with dropout)")
    else:
        print("⚠️ No significant difference (dropout might not be working)")
    
    # Test 2: Calculate R² in both modes
    print("\n🔍 TEST 2: R² CALCULATION IN DIFFERENT MODES")
    print("-" * 40)
    
    r2_train_mode = r2_score(y_train_scaled, pred_train_mode)
    r2_eval_mode = r2_score(y_train_scaled, pred_eval_mode)
    
    print(f"R² in train mode: {r2_train_mode:.6f}")
    print(f"R² in eval mode: {r2_eval_mode:.6f}")
    print(f"R² difference: {abs(r2_train_mode - r2_eval_mode):.6f}")
    
    # Test 3: Convert to original scale and check
    print("\n🔍 TEST 3: ORIGINAL SCALE METRICS")
    print("-" * 40)
    
    # Convert predictions to original scale
    def convert_to_original(pred_scaled):
        pred_log = target_scaler.inverse_transform(pred_scaled.reshape(-1, 1)).flatten()
        pred_orig = np.expm1(pred_log)
        return np.maximum(pred_orig, 0)
    
    pred_train_orig = convert_to_original(pred_train_mode)
    pred_eval_orig = convert_to_original(pred_eval_mode)
    
    r2_train_orig = r2_score(y_train_orig, pred_train_orig)
    r2_eval_orig = r2_score(y_train_orig, pred_eval_orig)
    
    print(f"Original scale R² (train mode): {r2_train_orig:.6f}")
    print(f"Original scale R² (eval mode): {r2_eval_orig:.6f}")
    print(f"Original scale R² difference: {abs(r2_train_orig - r2_eval_orig):.6f}")
    
    # Test 4: Simulate training loop behavior
    print("\n🔍 TEST 4: TRAINING LOOP SIMULATION")
    print("-" * 40)
    
    # Simulate what happens during training
    model.train()
    optimizer = torch.optim.AdamW(model.parameters(), lr=0.001)
    criterion = nn.MSELoss()
    
    # One training step
    optimizer.zero_grad()
    outputs = model(X_tensor)
    loss = criterion(outputs.squeeze(), y_tensor)
    loss.backward()
    optimizer.step()
    
    print(f"Training loss: {loss.item():.6f}")
    
    # Now calculate metrics the OLD way (eval mode) vs NEW way (train mode)
    print("\nOLD WAY (eval mode for metrics):")
    model.eval()
    with torch.no_grad():
        pred_old_way = model(X_tensor).squeeze().numpy()
    r2_old_way = r2_score(y_train_scaled, pred_old_way)
    print(f"R² (old way): {r2_old_way:.6f}")
    
    print("\nNEW WAY (train mode for metrics):")
    model.train()
    with torch.no_grad():
        pred_new_way = model(X_tensor).squeeze().numpy()
    r2_new_way = r2_score(y_train_scaled, pred_new_way)
    print(f"R² (new way): {r2_new_way:.6f}")
    
    print(f"\nDifference: {abs(r2_old_way - r2_new_way):.6f}")
    
    if abs(r2_old_way - r2_new_way) > 0.001:
        print("✅ Significant difference - fix is working!")
    else:
        print("⚠️ No significant difference - check dropout settings")
    
    return True

def main():
    """Run verification tests"""
    print("🧪 METRICS FIX VERIFICATION TEST")
    print("=" * 80)
    
    try:
        test_train_eval_mode_consistency()
        print("\n🎉 VERIFICATION COMPLETED")
    except Exception as e:
        print(f"❌ VERIFICATION FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
