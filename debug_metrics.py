import pandas as pd
import numpy as np

# Load the data to check ranges
train_df = pd.read_csv("Dataset/Dataset/train/train2.csv")
test_df = pd.read_csv("Dataset/Dataset/test/test2.csv")

print("=== DATA RANGE ANALYSIS ===")
print(f"Training QueryTime range: {train_df['QueryTime'].min():.2f} to {train_df['QueryTime'].max():.2f}")
print(f"Training QueryTime mean: {train_df['QueryTime'].mean():.2f}")
print(f"Training QueryTime std: {train_df['QueryTime'].std():.2f}")

print(f"\nTest QueryTime range: {test_df['QueryTime'].min():.2f} to {test_df['QueryTime'].max():.2f}")
print(f"Test QueryTime mean: {test_df['QueryTime'].mean():.2f}")
print(f"Test QueryTime std: {test_df['QueryTime'].std():.2f}")

print(f"\nTraining samples: {len(train_df)}")
print(f"Test samples: {len(test_df)}")

# Check for any extreme outliers
print(f"\nTraining QueryTime 95th percentile: {np.percentile(train_df['QueryTime'], 95):.2f}")
print(f"Training QueryTime 99th percentile: {np.percentile(train_df['QueryTime'], 99):.2f}")
print(f"Test QueryTime 95th percentile: {np.percentile(test_df['QueryTime'], 95):.2f}")
print(f"Test QueryTime 99th percentile: {np.percentile(test_df['QueryTime'], 99):.2f}")
