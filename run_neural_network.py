import subprocess
import sys

# Run the neural network model
if __name__ == "__main__":
    print("Starting Neural Network Training for Query Execution Time Prediction...")
    print("=" * 70)
    
    try:
        # Execute the neural network model
        result = subprocess.run([sys.executable, "neural_network_model.py"], 
                              capture_output=True, text=True, timeout=3600)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("\nSTDERR:")
            print(result.stderr)
            
        print(f"\nReturn code: {result.returncode}")
        
    except subprocess.TimeoutExpired:
        print("Training timed out after 1 hour")
    except Exception as e:
        print(f"Error running neural network: {e}")