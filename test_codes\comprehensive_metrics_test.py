#!/usr/bin/env python3
"""
Comprehensive test for all metric calculations in the neural network model
"""

import numpy as np
import torch
import torch.nn as nn
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import sys
import os

# Add parent directory to path to import the main module
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_r2_calculation():
    """Test R² calculation with known values"""
    print("🔍 TESTING R² CALCULATION")
    print("=" * 50)
    
    # Test case 1: Perfect prediction (R² should be 1.0)
    y_true = np.array([1, 2, 3, 4, 5])
    y_pred = np.array([1, 2, 3, 4, 5])
    r2 = r2_score(y_true, y_pred)
    print(f"Test 1 - Perfect prediction: R² = {r2:.6f} (Expected: 1.0)")
    assert abs(r2 - 1.0) < 1e-10, f"Expected 1.0, got {r2}"
    
    # Test case 2: Mean prediction (R² should be 0.0)
    y_true = np.array([1, 2, 3, 4, 5])
    y_pred = np.array([3, 3, 3, 3, 3])  # All predictions = mean
    r2 = r2_score(y_true, y_pred)
    print(f"Test 2 - Mean prediction: R² = {r2:.6f} (Expected: 0.0)")
    assert abs(r2 - 0.0) < 1e-10, f"Expected 0.0, got {r2}"
    
    # Test case 3: Worse than mean (R² should be negative)
    y_true = np.array([1, 2, 3, 4, 5])
    y_pred = np.array([5, 4, 3, 2, 1])  # Opposite pattern
    r2 = r2_score(y_true, y_pred)
    print(f"Test 3 - Worse than mean: R² = {r2:.6f} (Expected: negative)")
    assert r2 < 0, f"Expected negative, got {r2}"
    
    # Test case 4: Large values (test numerical stability)
    y_true = np.array([1000000, 2000000, 3000000, 4000000, 5000000])
    y_pred = np.array([1100000, 1900000, 3100000, 3900000, 5100000])
    r2 = r2_score(y_true, y_pred)
    print(f"Test 4 - Large values: R² = {r2:.6f} (Should be reasonable)")
    
    print("✅ R² calculation tests PASSED\n")

def test_preprocessing_pipeline():
    """Test the preprocessing pipeline for consistency"""
    print("🔍 TESTING PREPROCESSING PIPELINE")
    print("=" * 50)
    
    # Create test data
    original_values = np.array([45.0, 100.0, 500.0, 1000.0, 5000.0])
    print(f"Original values: {original_values}")
    
    # Step 1: Log transformation
    log_values = np.log1p(original_values)
    print(f"After log1p: {log_values}")
    
    # Step 2: StandardScaler
    scaler = StandardScaler()
    scaled_values = scaler.fit_transform(log_values.reshape(-1, 1)).flatten()
    print(f"After scaling: {scaled_values}")
    
    # Step 3: Inverse transformation
    unscaled_values = scaler.inverse_transform(scaled_values.reshape(-1, 1)).flatten()
    print(f"After inverse scaling: {unscaled_values}")
    
    # Step 4: Inverse log transformation
    recovered_values = np.expm1(unscaled_values)
    print(f"Recovered values: {recovered_values}")
    
    # Check if we recovered original values
    diff = np.abs(original_values - recovered_values)
    max_diff = np.max(diff)
    print(f"Maximum difference: {max_diff}")
    
    if max_diff < 1e-10:
        print("✅ Preprocessing pipeline is CONSISTENT\n")
    else:
        print("❌ Preprocessing pipeline has ERRORS\n")
        return False
    
    return True

def test_metric_calculation_with_real_data():
    """Test metric calculation with realistic neural network data"""
    print("🔍 TESTING METRICS WITH REALISTIC DATA")
    print("=" * 50)
    
    # Simulate realistic query time data
    np.random.seed(42)
    n_samples = 100
    
    # Generate realistic query times (45ms to 5M ms)
    true_times = np.random.lognormal(mean=6, sigma=2, size=n_samples)
    true_times = np.clip(true_times, 45, 5000000)
    
    # Simulate predictions with some error
    noise = np.random.normal(0, 0.1, n_samples)
    pred_times = true_times * (1 + noise)
    pred_times = np.maximum(pred_times, 0)  # Ensure non-negative
    
    print(f"True times range: [{true_times.min():.1f}, {true_times.max():.1f}]")
    print(f"Pred times range: [{pred_times.min():.1f}, {pred_times.max():.1f}]")
    
    # Calculate metrics
    r2 = r2_score(true_times, pred_times)
    rmse = np.sqrt(mean_squared_error(true_times, pred_times))
    mae = mean_absolute_error(true_times, pred_times)
    
    print(f"R² Score: {r2:.6f}")
    print(f"RMSE: {rmse:.2f}")
    print(f"MAE: {mae:.2f}")
    
    # Validate ranges
    assert -10 <= r2 <= 1.0, f"R² out of reasonable range: {r2}"
    assert rmse >= 0, f"RMSE should be non-negative: {rmse}"
    assert mae >= 0, f"MAE should be non-negative: {mae}"
    
    print("✅ Realistic data metrics test PASSED\n")

def test_training_vs_eval_mode():
    """Test if model predictions differ between train and eval mode"""
    print("🔍 TESTING TRAIN VS EVAL MODE PREDICTIONS")
    print("=" * 50)
    
    # Create a simple model with dropout
    class TestModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.layers = nn.Sequential(
                nn.Linear(10, 32),
                nn.Tanh(),
                nn.Dropout(0.1),
                nn.Linear(32, 1)
            )
        
        def forward(self, x):
            return self.layers(x)
    
    model = TestModel()
    
    # Create test input
    x = torch.randn(5, 10)
    
    # Get predictions in train mode
    model.train()
    with torch.no_grad():
        pred_train = model(x)
    
    # Get predictions in eval mode
    model.eval()
    with torch.no_grad():
        pred_eval = model(x)
    
    # Check if predictions are different (they should be due to dropout)
    diff = torch.abs(pred_train - pred_eval).max().item()
    print(f"Max difference between train/eval predictions: {diff:.6f}")
    
    if diff > 1e-6:
        print("✅ Train/Eval modes produce different predictions (expected with dropout)")
    else:
        print("⚠️ Train/Eval modes produce identical predictions (dropout might not be working)")
    
    print()

def test_loss_vs_metrics_consistency():
    """Test consistency between loss calculation and metric calculation"""
    print("🔍 TESTING LOSS VS METRICS CONSISTENCY")
    print("=" * 50)
    
    # Create test data
    y_true = torch.tensor([1.0, 2.0, 3.0, 4.0, 5.0])
    y_pred = torch.tensor([1.1, 1.9, 3.1, 3.9, 5.1])
    
    # Calculate MSE loss (PyTorch)
    mse_loss = nn.MSELoss()
    loss_value = mse_loss(y_pred, y_true).item()
    
    # Calculate MSE metric (sklearn)
    mse_metric = mean_squared_error(y_true.numpy(), y_pred.numpy())
    
    print(f"PyTorch MSE Loss: {loss_value:.6f}")
    print(f"Sklearn MSE Metric: {mse_metric:.6f}")
    print(f"Difference: {abs(loss_value - mse_metric):.10f}")
    
    if abs(loss_value - mse_metric) < 1e-10:
        print("✅ Loss and metrics are CONSISTENT\n")
    else:
        print("❌ Loss and metrics are INCONSISTENT\n")
        return False
    
    return True

def main():
    """Run all metric validation tests"""
    print("🧪 COMPREHENSIVE METRICS VALIDATION TEST")
    print("=" * 80)
    
    try:
        # Run all tests
        test_r2_calculation()
        test_preprocessing_pipeline()
        test_metric_calculation_with_real_data()
        test_training_vs_eval_mode()
        test_loss_vs_metrics_consistency()
        
        print("🎉 ALL TESTS PASSED - METRICS ARE WORKING CORRECTLY!")
        
    except Exception as e:
        print(f"❌ TEST FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
