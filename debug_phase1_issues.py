#!/usr/bin/env python3
"""
Debug Phase 1 Issues - Identify problems with high RMSE, MAE, MAPE
"""

import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt

def debug_data_preprocessing():
    """Debug the data preprocessing pipeline"""
    print("="*60)
    print("DEBUGGING DATA PREPROCESSING")
    print("="*60)
    
    # Load data
    train_df = pd.read_csv("Dataset/Dataset/train/train.csv")
    
    # Essential features
    essential_features = [
        'EstimatedTotalSubtreeCostComputeScalar',
        'EstimateRowsHashMatch', 
        'EstimatedTotalSubtreeCostNestedLoops',
        'EstimateCPUStreamAggregate',
        'EstimatedTotalSubtreeCostClusteredIndexSeek',
        'EstimatedTotalSubtreeCostStreamAggregate',
        'EstimateCPUClusteredIndexScan',
        'EstimateRowsClusteredIndexScan',
        'EstimatedTotalSubtreeCostSort',
        'EstimateCPUNestedLoops'
    ]
    
    # Extract data
    X_full = train_df[essential_features].fillna(0)
    y_full = train_df['QueryTime']
    
    print(f"Original target statistics:")
    print(f"Min: {y_full.min():.2f}")
    print(f"Max: {y_full.max():,.2f}")
    print(f"Mean: {y_full.mean():,.2f}")
    print(f"Median: {y_full.median():,.2f}")
    print(f"Std: {y_full.std():,.2f}")
    print(f"Skewness: {y_full.skew():.3f}")
    
    # Log transform
    y_log = np.log1p(y_full)
    print(f"\nAfter log1p transform:")
    print(f"Min: {y_log.min():.3f}")
    print(f"Max: {y_log.max():.3f}")
    print(f"Mean: {y_log.mean():.3f}")
    print(f"Median: {y_log.median():.3f}")
    print(f"Std: {y_log.std():.3f}")
    print(f"Skewness: {y_log.skew():.3f}")
    
    # Train-validation split
    X_train, X_val, y_train_log, y_val_log = train_test_split(
        X_full, y_log, test_size=0.2, random_state=42
    )
    
    # Get original values for validation set
    y_train_orig = train_df.loc[X_train.index, 'QueryTime']
    y_val_orig = train_df.loc[X_val.index, 'QueryTime']
    
    print(f"\nValidation set original target statistics:")
    print(f"Min: {y_val_orig.min():.2f}")
    print(f"Max: {y_val_orig.max():,.2f}")
    print(f"Mean: {y_val_orig.mean():,.2f}")
    print(f"Median: {y_val_orig.median():.2f}")
    
    # Target scaling
    target_scaler = StandardScaler()
    y_train_scaled = target_scaler.fit_transform(y_train_log.values.reshape(-1, 1)).flatten()
    y_val_scaled = target_scaler.transform(y_val_log.values.reshape(-1, 1)).flatten()
    
    print(f"\nAfter StandardScaler on log-transformed target:")
    print(f"Training - Mean: {y_train_scaled.mean():.6f}, Std: {y_train_scaled.std():.6f}")
    print(f"Validation - Mean: {y_val_scaled.mean():.6f}, Std: {y_val_scaled.std():.6f}")
    
    return X_train, X_val, y_train_scaled, y_val_scaled, y_val_orig, target_scaler

def debug_inverse_transform(target_scaler, y_val_orig):
    """Debug the inverse transformation process"""
    print("\n" + "="*60)
    print("DEBUGGING INVERSE TRANSFORMATION")
    print("="*60)
    
    # Test with known values
    test_orig = np.array([1000, 5000, 10000, 50000])  # Test values in ms
    print(f"Test original values: {test_orig}")
    
    # Forward transform
    test_log = np.log1p(test_orig)
    print(f"After log1p: {test_log}")
    
    test_scaled = target_scaler.transform(test_log.reshape(-1, 1)).flatten()
    print(f"After scaling: {test_scaled}")
    
    # Inverse transform
    test_unscaled = target_scaler.inverse_transform(test_scaled.reshape(-1, 1)).flatten()
    print(f"After inverse scaling: {test_unscaled}")
    
    test_final = np.expm1(test_unscaled)
    print(f"After expm1: {test_final}")
    
    print(f"Reconstruction error: {np.abs(test_orig - test_final)}")
    print(f"Max reconstruction error: {np.max(np.abs(test_orig - test_final)):.6f}")
    
    # Test with validation data
    print(f"\nTesting with actual validation data:")
    y_val_log_orig = np.log1p(y_val_orig)
    y_val_scaled_test = target_scaler.transform(y_val_log_orig.values.reshape(-1, 1)).flatten()
    
    # Inverse transform
    y_val_reconstructed_log = target_scaler.inverse_transform(y_val_scaled_test.reshape(-1, 1)).flatten()
    y_val_reconstructed = np.expm1(y_val_reconstructed_log)
    
    reconstruction_error = np.abs(y_val_orig - y_val_reconstructed)
    print(f"Validation reconstruction error - Mean: {reconstruction_error.mean():.6f}")
    print(f"Validation reconstruction error - Max: {reconstruction_error.max():.6f}")
    
    return test_scaled

def debug_simple_predictions(X_val, y_val_scaled, y_val_orig, target_scaler):
    """Debug with simple predictions"""
    print("\n" + "="*60)
    print("DEBUGGING WITH SIMPLE PREDICTIONS")
    print("="*60)
    
    # Test 1: Perfect predictions (predict exactly the scaled target)
    print("Test 1: Perfect scaled predictions")
    perfect_pred_orig = inverse_transform_predictions(y_val_scaled, target_scaler)
    
    rmse_perfect = np.sqrt(mean_squared_error(y_val_orig, perfect_pred_orig))
    mae_perfect = mean_absolute_error(y_val_orig, perfect_pred_orig)
    r2_perfect = r2_score(y_val_orig, perfect_pred_orig)
    
    print(f"Perfect predictions - RMSE: {rmse_perfect:.2f}, MAE: {mae_perfect:.2f}, R²: {r2_perfect:.6f}")
    
    # Test 2: Mean predictions
    print("\nTest 2: Mean predictions")
    mean_pred_scaled = np.full_like(y_val_scaled, y_val_scaled.mean())
    mean_pred_orig = inverse_transform_predictions(mean_pred_scaled, target_scaler)
    
    rmse_mean = np.sqrt(mean_squared_error(y_val_orig, mean_pred_orig))
    mae_mean = mean_absolute_error(y_val_orig, mean_pred_orig)
    r2_mean = r2_score(y_val_orig, mean_pred_orig)
    
    print(f"Mean predictions - RMSE: {rmse_mean:.2f}, MAE: {mae_mean:.2f}, R²: {r2_mean:.6f}")
    
    # Test 3: Random predictions around mean
    print("\nTest 3: Random predictions")
    np.random.seed(42)
    random_pred_scaled = np.random.normal(y_val_scaled.mean(), y_val_scaled.std(), len(y_val_scaled))
    random_pred_orig = inverse_transform_predictions(random_pred_scaled, target_scaler)
    
    rmse_random = np.sqrt(mean_squared_error(y_val_orig, random_pred_orig))
    mae_random = mean_absolute_error(y_val_orig, random_pred_orig)
    r2_random = r2_score(y_val_orig, random_pred_orig)
    
    print(f"Random predictions - RMSE: {rmse_random:.2f}, MAE: {mae_random:.2f}, R²: {r2_random:.6f}")
    
    # Analyze the ranges
    print(f"\nPrediction ranges:")
    print(f"Perfect: {perfect_pred_orig.min():.2f} to {perfect_pred_orig.max():.2f}")
    print(f"Mean: {mean_pred_orig.min():.2f} to {mean_pred_orig.max():.2f}")
    print(f"Random: {random_pred_orig.min():.2f} to {random_pred_orig.max():.2f}")
    print(f"Actual: {y_val_orig.min():.2f} to {y_val_orig.max():.2f}")

def inverse_transform_predictions(predictions, target_scaler):
    """Convert scaled predictions back to original scale"""
    # Inverse scale
    pred_log = target_scaler.inverse_transform(np.array(predictions).reshape(-1, 1)).flatten()
    # Inverse log transform
    pred_orig = np.expm1(pred_log)
    # Ensure non-negative
    pred_orig = np.maximum(pred_orig, 0)
    return pred_orig

def debug_feature_scaling(X_train, X_val):
    """Debug feature scaling issues"""
    print("\n" + "="*60)
    print("DEBUGGING FEATURE SCALING")
    print("="*60)
    
    print("Original feature statistics:")
    print(f"Training features - Min: {X_train.min().min():.2f}, Max: {X_train.max().max():.2f}")
    print(f"Training features - Mean: {X_train.mean().mean():.2f}, Std: {X_train.std().mean():.2f}")
    
    # Apply RobustScaler
    feature_scaler = RobustScaler()
    X_train_scaled = feature_scaler.fit_transform(X_train)
    X_val_scaled = feature_scaler.transform(X_val)
    
    print(f"\nAfter RobustScaler:")
    print(f"Training features - Min: {X_train_scaled.min():.2f}, Max: {X_train_scaled.max():.2f}")
    print(f"Training features - Mean: {X_train_scaled.mean():.2f}, Std: {X_train_scaled.std():.2f}")
    print(f"Validation features - Min: {X_val_scaled.min():.2f}, Max: {X_val_scaled.max():.2f}")
    print(f"Validation features - Mean: {X_val_scaled.mean():.2f}, Std: {X_val_scaled.std():.2f}")
    
    # Check for extreme values
    extreme_threshold = 10
    extreme_train = np.abs(X_train_scaled) > extreme_threshold
    extreme_val = np.abs(X_val_scaled) > extreme_threshold
    
    print(f"\nExtreme values (>|{extreme_threshold}|):")
    print(f"Training: {extreme_train.sum()} values")
    print(f"Validation: {extreme_val.sum()} values")
    
    if extreme_train.sum() > 0:
        print(f"Max extreme training value: {np.abs(X_train_scaled).max():.2f}")
    if extreme_val.sum() > 0:
        print(f"Max extreme validation value: {np.abs(X_val_scaled).max():.2f}")
    
    return X_train_scaled, X_val_scaled

def analyze_target_distribution():
    """Analyze target distribution in detail"""
    print("\n" + "="*60)
    print("DETAILED TARGET DISTRIBUTION ANALYSIS")
    print("="*60)
    
    train_df = pd.read_csv("Dataset/Dataset/train/train.csv")
    y = train_df['QueryTime']
    
    # Percentiles
    percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99, 99.9]
    print("Target percentiles:")
    for p in percentiles:
        print(f"  {p:4.1f}%: {np.percentile(y, p):10,.2f} ms")
    
    # Check for outliers
    q1 = np.percentile(y, 25)
    q3 = np.percentile(y, 75)
    iqr = q3 - q1
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    
    outliers = (y < lower_bound) | (y > upper_bound)
    print(f"\nOutlier analysis:")
    print(f"IQR: {iqr:,.2f}")
    print(f"Lower bound: {lower_bound:,.2f}")
    print(f"Upper bound: {upper_bound:,.2f}")
    print(f"Outliers: {outliers.sum()} ({outliers.mean()*100:.1f}%)")
    
    # Extreme values
    extreme_threshold = np.percentile(y, 99)
    extreme_values = y > extreme_threshold
    print(f"\nExtreme values (>99th percentile = {extreme_threshold:,.2f}):")
    print(f"Count: {extreme_values.sum()}")
    print(f"Max: {y.max():,.2f}")
    print(f"Ratio max/99th: {y.max()/extreme_threshold:.1f}x")

def main():
    """Main debugging function"""
    print("DEBUGGING PHASE 1 HIGH RMSE/MAE/MAPE ISSUES")
    print("="*80)
    
    # 1. Analyze target distribution
    analyze_target_distribution()
    
    # 2. Debug data preprocessing
    X_train, X_val, y_train_scaled, y_val_scaled, y_val_orig, target_scaler = debug_data_preprocessing()
    
    # 3. Debug feature scaling
    X_train_scaled, X_val_scaled = debug_feature_scaling(X_train, X_val)
    
    # 4. Debug inverse transformation
    debug_inverse_transform(target_scaler, y_val_orig)
    
    # 5. Debug with simple predictions
    debug_simple_predictions(X_val_scaled, y_val_scaled, y_val_orig, target_scaler)
    
    print("\n" + "="*80)
    print("DEBUGGING COMPLETED")
    print("="*80)

if __name__ == "__main__":
    main()
