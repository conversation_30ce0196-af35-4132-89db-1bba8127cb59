
select top 100 i_product_name
from item i1
where i_manufact_id between 690 and 690+40
and (select count(*) as item_cnt
from item
where (i_manufact = i1.i_manufact and
((i_category = 'Women' and
(i_color = 'magenta' or i_color = 'orchid') and
(i_units = 'N/A' or i_units = 'Bunch') and
(i_size = 'small' or i_size = 'medium')
) or
(i_category = 'Women' and
(i_color = 'royal' or i_color = 'light') and
(i_units = 'Gram' or i_units = 'Bundle') and
(i_size = 'large' or i_size = 'economy')
) or
(i_category = 'Men' and
(i_color = 'burlywood' or i_color = 'dark') and
(i_units = 'Cup' or i_units = 'Ton') and
(i_size = 'extra large' or i_size = 'N/A')
) or
(i_category = 'Men' and
(i_color = 'chocolate' or i_color = 'papaya') and
(i_units = 'Box' or i_units = 'Lb') and
(i_size = 'small' or i_size = 'medium')
))) or
(i_manufact = i1.i_manufact and
((i_category = 'Women' and
(i_color = 'cornflower' or i_color = 'cream') and
(i_units = 'Ounce' or i_units = 'Carton') and
(i_size = 'small' or i_size = 'medium')
) or
(i_category = 'Women' and
(i_color = 'sandy' or i_color = 'tomato') and
(i_units = 'Dram' or i_units = 'Unknown') and
(i_size = 'large' or i_size = 'economy')
) or
(i_category = 'Men' and
(i_color = 'snow' or i_color = 'frosted') and
(i_units = 'Tbl' or i_units = 'Dozen') and
(i_size = 'extra large' or i_size = 'N/A')
) or
(i_category = 'Men' and
(i_color = 'sienna' or i_color = 'blue') and
(i_units = 'Case' or i_units = 'Each') and
(i_size = 'small' or i_size = 'medium')
)))) > 0
order by i_product_name
