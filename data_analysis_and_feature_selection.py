#!/usr/bin/env python3
"""
Comprehensive Data Analysis and Feature Selection for Query Execution Time Prediction
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.feature_selection import mutual_info_regression, SelectKBest, f_regression
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LassoCV
from scipy import stats
from scipy.stats import pearsonr, spearmanr
import warnings
warnings.filterwarnings('ignore')

class QueryDataAnalyzer:
    def __init__(self, train_path, test_path):
        self.train_path = train_path
        self.test_path = test_path
        self.train_df = None
        self.test_df = None
        self.feature_importance_results = {}
        
    def load_data(self):
        """Load training and test datasets"""
        print("Loading datasets...")
        self.train_df = pd.read_csv(self.train_path)
        self.test_df = pd.read_csv(self.test_path)
        
        print(f"Training data shape: {self.train_df.shape}")
        print(f"Test data shape: {self.test_df.shape}")
        print(f"Features: {len(self.train_df.columns) - 1}")
        
        return self.train_df, self.test_df
    
    def basic_data_analysis(self):
        """Perform basic data analysis"""
        print("\n" + "="*60)
        print("BASIC DATA ANALYSIS")
        print("="*60)
        
        # Target variable analysis
        target = self.train_df['QueryTime']
        print(f"\nTarget Variable (QueryTime) Statistics:")
        print(f"Count: {len(target):,}")
        print(f"Mean: {target.mean():,.2f} ms")
        print(f"Median: {target.median():,.2f} ms")
        print(f"Std: {target.std():,.2f} ms")
        print(f"Min: {target.min():,.2f} ms")
        print(f"Max: {target.max():,.2f} ms")
        print(f"Range: {target.max() - target.min():,.2f} ms")
        print(f"Skewness: {stats.skew(target):.3f}")
        print(f"Kurtosis: {stats.kurtosis(target):.3f}")
        
        # Percentiles
        percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
        print(f"\nPercentiles:")
        for p in percentiles:
            print(f"  {p}th: {np.percentile(target, p):,.2f} ms")
        
        # Feature analysis
        features = [col for col in self.train_df.columns if col != 'QueryTime']
        print(f"\nFeature Analysis:")
        print(f"Total features: {len(features)}")
        
        # Group features by operation type
        operation_groups = self._group_features_by_operation(features)
        for op_type, op_features in operation_groups.items():
            print(f"  {op_type}: {len(op_features)} features")
        
        return target, features, operation_groups
    
    def _group_features_by_operation(self, features):
        """Group features by SQL operation type"""
        operation_groups = {
            'ClusteredIndexScan': [],
            'ClusteredIndexSeek': [],
            'ComputeScalar': [],
            'Concatenation': [],
            'Filter': [],
            'HashMatch': [],
            'NestedLoops': [],
            'Top': [],
            'Segment': [],
            'Sort': [],
            'RowCountSpool': [],
            'StreamAggregate': [],
            'TableSpool': [],
            'MergeJoin': [],
            'Assert': [],
            'SequenceProject': [],
            'IndexSpool': []
        }
        
        for feature in features:
            for op_type in operation_groups.keys():
                if op_type in feature:
                    operation_groups[op_type].append(feature)
                    break
        
        return operation_groups
    
    def correlation_analysis(self, target, features):
        """Analyze correlations with target variable"""
        print("\n" + "="*60)
        print("CORRELATION ANALYSIS")
        print("="*60)
        
        correlations = []
        
        for feature in features:
            # Handle missing values
            feature_data = self.train_df[feature].fillna(0)
            
            # Pearson correlation
            pearson_corr, pearson_p = pearsonr(feature_data, target)
            
            # Spearman correlation
            spearman_corr, spearman_p = spearmanr(feature_data, target)
            
            correlations.append({
                'feature': feature,
                'pearson_corr': pearson_corr,
                'pearson_p': pearson_p,
                'spearman_corr': spearman_corr,
                'spearman_p': spearman_p,
                'abs_pearson': abs(pearson_corr),
                'abs_spearman': abs(spearman_corr)
            })
        
        # Convert to DataFrame and sort
        corr_df = pd.DataFrame(correlations)
        corr_df = corr_df.sort_values('abs_pearson', ascending=False)
        
        print("Top 20 Features by Absolute Pearson Correlation:")
        print("-" * 50)
        for i, row in corr_df.head(20).iterrows():
            print(f"{row['feature'][:50]:50} | {row['pearson_corr']:7.4f} | {row['spearman_corr']:7.4f}")
        
        self.feature_importance_results['correlation'] = corr_df
        return corr_df
    
    def mutual_information_analysis(self, target, features):
        """Analyze mutual information with target variable"""
        print("\n" + "="*60)
        print("MUTUAL INFORMATION ANALYSIS")
        print("="*60)
        
        # Prepare feature matrix
        X = self.train_df[features].fillna(0)
        
        # Calculate mutual information
        mi_scores = mutual_info_regression(X, target, random_state=42)
        
        # Create results DataFrame
        mi_df = pd.DataFrame({
            'feature': features,
            'mutual_info': mi_scores
        }).sort_values('mutual_info', ascending=False)
        
        print("Top 20 Features by Mutual Information:")
        print("-" * 50)
        for i, row in mi_df.head(20).iterrows():
            print(f"{row['feature'][:50]:50} | {row['mutual_info']:10.6f}")
        
        self.feature_importance_results['mutual_info'] = mi_df
        return mi_df
    
    def random_forest_importance(self, target, features):
        """Analyze feature importance using Random Forest"""
        print("\n" + "="*60)
        print("RANDOM FOREST FEATURE IMPORTANCE")
        print("="*60)
        
        # Prepare data
        X = self.train_df[features].fillna(0)
        
        # Scale features for better performance
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Train Random Forest
        rf = RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1)
        rf.fit(X_scaled, target)
        
        # Get feature importance
        rf_df = pd.DataFrame({
            'feature': features,
            'rf_importance': rf.feature_importances_
        }).sort_values('rf_importance', ascending=False)
        
        print("Top 20 Features by Random Forest Importance:")
        print("-" * 50)
        for i, row in rf_df.head(20).iterrows():
            print(f"{row['feature'][:50]:50} | {row['rf_importance']:10.6f}")
        
        self.feature_importance_results['random_forest'] = rf_df
        return rf_df
    
    def lasso_feature_selection(self, target, features):
        """Analyze feature importance using Lasso regression"""
        print("\n" + "="*60)
        print("LASSO FEATURE SELECTION")
        print("="*60)
        
        # Prepare data
        X = self.train_df[features].fillna(0)
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Apply Lasso with cross-validation
        lasso = LassoCV(cv=5, random_state=42, max_iter=2000)
        lasso.fit(X_scaled, target)
        
        # Get coefficients
        lasso_df = pd.DataFrame({
            'feature': features,
            'lasso_coef': lasso.coef_,
            'abs_lasso_coef': np.abs(lasso.coef_)
        }).sort_values('abs_lasso_coef', ascending=False)
        
        # Filter non-zero coefficients
        selected_features = lasso_df[lasso_df['abs_lasso_coef'] > 0]
        
        print(f"Lasso selected {len(selected_features)} features out of {len(features)}")
        print(f"Alpha used: {lasso.alpha_:.6f}")
        print("\nTop 20 Features by Absolute Lasso Coefficient:")
        print("-" * 50)
        for i, row in selected_features.head(20).iterrows():
            print(f"{row['feature'][:50]:50} | {row['lasso_coef']:10.6f}")
        
        self.feature_importance_results['lasso'] = lasso_df
        return lasso_df, selected_features
    
    def operation_type_analysis(self, target, operation_groups):
        """Analyze importance by operation type"""
        print("\n" + "="*60)
        print("OPERATION TYPE ANALYSIS")
        print("="*60)
        
        operation_importance = {}
        
        for op_type, op_features in operation_groups.items():
            if not op_features:
                continue
                
            # Calculate average correlation for this operation type
            correlations = []
            for feature in op_features:
                feature_data = self.train_df[feature].fillna(0)
                corr, _ = pearsonr(feature_data, target)
                correlations.append(abs(corr))
            
            avg_correlation = np.mean(correlations)
            max_correlation = np.max(correlations)
            
            # Calculate how often this operation appears (non-zero values)
            operation_counts = []
            for feature in op_features:
                if 'OpCount' in feature:
                    non_zero_count = (self.train_df[feature] > 0).sum()
                    operation_counts.append(non_zero_count)
            
            avg_usage = np.mean(operation_counts) if operation_counts else 0
            
            operation_importance[op_type] = {
                'num_features': len(op_features),
                'avg_correlation': avg_correlation,
                'max_correlation': max_correlation,
                'avg_usage': avg_usage,
                'usage_percentage': (avg_usage / len(self.train_df)) * 100
            }
        
        # Sort by average correlation
        sorted_ops = sorted(operation_importance.items(), 
                          key=lambda x: x[1]['avg_correlation'], reverse=True)
        
        print("Operation Types by Average Correlation with QueryTime:")
        print("-" * 80)
        print(f"{'Operation':<20} | {'Features':<8} | {'Avg Corr':<10} | {'Max Corr':<10} | {'Usage %':<8}")
        print("-" * 80)
        
        for op_type, stats in sorted_ops:
            if stats['num_features'] > 0:
                print(f"{op_type:<20} | {stats['num_features']:<8} | "
                      f"{stats['avg_correlation']:<10.4f} | {stats['max_correlation']:<10.4f} | "
                      f"{stats['usage_percentage']:<8.1f}")
        
        return operation_importance
    
    def create_feature_ranking(self):
        """Create comprehensive feature ranking"""
        print("\n" + "="*60)
        print("COMPREHENSIVE FEATURE RANKING")
        print("="*60)
        
        # Get all feature importance results
        corr_df = self.feature_importance_results['correlation']
        mi_df = self.feature_importance_results['mutual_info']
        rf_df = self.feature_importance_results['random_forest']
        lasso_df = self.feature_importance_results['lasso']
        
        # Create ranking for each method
        corr_df['corr_rank'] = corr_df['abs_pearson'].rank(ascending=False)
        mi_df['mi_rank'] = mi_df['mutual_info'].rank(ascending=False)
        rf_df['rf_rank'] = rf_df['rf_importance'].rank(ascending=False)
        lasso_df['lasso_rank'] = lasso_df['abs_lasso_coef'].rank(ascending=False)
        
        # Merge all rankings
        final_ranking = corr_df[['feature', 'abs_pearson', 'corr_rank']].copy()
        final_ranking = final_ranking.merge(mi_df[['feature', 'mutual_info', 'mi_rank']], on='feature')
        final_ranking = final_ranking.merge(rf_df[['feature', 'rf_importance', 'rf_rank']], on='feature')
        final_ranking = final_ranking.merge(lasso_df[['feature', 'abs_lasso_coef', 'lasso_rank']], on='feature')
        
        # Calculate average rank
        final_ranking['avg_rank'] = (final_ranking['corr_rank'] + final_ranking['mi_rank'] + 
                                   final_ranking['rf_rank'] + final_ranking['lasso_rank']) / 4
        
        # Sort by average rank
        final_ranking = final_ranking.sort_values('avg_rank')
        
        print("Top 30 Features by Average Ranking:")
        print("-" * 100)
        print(f"{'Feature':<50} | {'Corr':<8} | {'MI':<10} | {'RF':<10} | {'Lasso':<10} | {'Avg Rank':<8}")
        print("-" * 100)
        
        for i, row in final_ranking.head(30).iterrows():
            print(f"{row['feature'][:49]:<50} | {row['abs_pearson']:<8.4f} | "
                  f"{row['mutual_info']:<10.6f} | {row['rf_importance']:<10.6f} | "
                  f"{row['abs_lasso_coef']:<10.6f} | {row['avg_rank']:<8.1f}")
        
        return final_ranking
    
    def generate_recommendations(self, final_ranking, operation_importance):
        """Generate feature selection recommendations"""
        print("\n" + "="*60)
        print("FEATURE SELECTION RECOMMENDATIONS")
        print("="*60)
        
        # Top features by different criteria
        top_10_overall = final_ranking.head(10)['feature'].tolist()
        top_20_overall = final_ranking.head(20)['feature'].tolist()
        top_30_overall = final_ranking.head(30)['feature'].tolist()
        
        # High correlation features
        high_corr_features = final_ranking[final_ranking['abs_pearson'] > 0.3]['feature'].tolist()
        
        # Features selected by Lasso
        lasso_selected = final_ranking[final_ranking['abs_lasso_coef'] > 0]['feature'].tolist()
        
        print("RECOMMENDATION SUMMARY:")
        print("-" * 40)
        print(f"1. TOP 10 FEATURES (Best overall ranking):")
        for i, feature in enumerate(top_10_overall, 1):
            print(f"   {i:2d}. {feature}")
        
        print(f"\n2. TOP 20 FEATURES (Good balance of performance/complexity):")
        for i, feature in enumerate(top_20_overall, 1):
            print(f"   {i:2d}. {feature}")
        
        print(f"\n3. HIGH CORRELATION FEATURES (|r| > 0.3): {len(high_corr_features)} features")
        print(f"4. LASSO SELECTED FEATURES: {len(lasso_selected)} features")
        
        # Most important operation types
        top_operations = sorted(operation_importance.items(), 
                              key=lambda x: x[1]['avg_correlation'], reverse=True)[:5]
        
        print(f"\n5. MOST IMPORTANT OPERATION TYPES:")
        for i, (op_type, stats) in enumerate(top_operations, 1):
            print(f"   {i}. {op_type} (avg corr: {stats['avg_correlation']:.4f})")
        
        recommendations = {
            'top_10': top_10_overall,
            'top_20': top_20_overall,
            'top_30': top_30_overall,
            'high_correlation': high_corr_features,
            'lasso_selected': lasso_selected,
            'top_operations': [op[0] for op in top_operations]
        }
        
        return recommendations

def main():
    """Main analysis function"""
    # Initialize analyzer
    analyzer = QueryDataAnalyzer(
        "Dataset/Dataset/train/train.csv",
        "Dataset/Dataset/test/test.csv"
    )
    
    # Load data
    train_df, test_df = analyzer.load_data()
    
    # Basic analysis
    target, features, operation_groups = analyzer.basic_data_analysis()
    
    # Feature importance analyses
    corr_df = analyzer.correlation_analysis(target, features)
    mi_df = analyzer.mutual_information_analysis(target, features)
    rf_df = analyzer.random_forest_importance(target, features)
    lasso_df, selected_features = analyzer.lasso_feature_selection(target, features)
    
    # Operation type analysis
    operation_importance = analyzer.operation_type_analysis(target, operation_groups)
    
    # Create comprehensive ranking
    final_ranking = analyzer.create_feature_ranking()
    
    # Generate recommendations
    recommendations = analyzer.generate_recommendations(final_ranking, operation_importance)
    
    # Save results
    final_ranking.to_csv('feature_analysis_results.csv', index=False)
    print(f"\nResults saved to 'feature_analysis_results.csv'")
    
    return analyzer, recommendations

if __name__ == "__main__":
    analyzer, recommendations = main()
