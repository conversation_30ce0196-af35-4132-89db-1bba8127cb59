# Train-Test Only Model Results Summary

## 🎯 **Training Configuration**
- **Epochs**: 200
- **Batch Size**: 32
- **Learning Rate**: 0.001
- **Training Approach**: Train-Test Only (no validation set)
- **Features**: 10 specific features from train2.csv and test2.csv
- **Timestamp**: 20250805_102305

## 📊 **Final Performance Results**

| Metric | Value | Status |
|--------|-------|--------|
| **R² Score** | **0.7393** | ✅ Good |
| **RMSE** | **3,555 ms** | ✅ Good |
| **MAE** | **2,653 ms** | ✅ Good |
| **MAPE** | **46.57%** | ✅ Good |
| **Pearson Correlation** | **0.8618** | ✅ Good |
| **Spearman Correlation** | **0.8781** | ✅ Good |

## 🎯 **Features Used (10 Specific Features)**
1. EstimatedTotalSubtreeCostHashMatch
2. EstimateRowsHashMatch
3. total_num_joins
4. ClusteredIndexScanOpCount
5. ClusteredIndexSeekOpCount
6. SortOpCount
7. total_estimated_cpu_cost
8. total_estimated_io_cost
9. EstimateRowsSort
10. HashMatchOpCount

## 🚀 **Model Architecture**
- **Input Features**: 10
- **Hidden Layers**: [128, 64, 32]
- **Total Parameters**: 12,225
- **Activation**: Tanh
- **Loss Function**: MSE Loss
- **Optimizer**: AdamW

## 📈 **Training Results**
- **Epochs Trained**: 200
- **Final Train Loss**: 0.932778
- **Final Test R²**: 0.8473
- **Final Test RMSE**: 2720.39 ms

## 📁 **Output Files**
- **Model**: output/train_test_only_model_20250805_102305.pth
- **Results**: output/train_test_only_results_20250805_102305.json
- **Visualization**: output/train_test_only_results_20250805_102305.png
- **Best Model**: output/best_train_test_only_model.pth

## 🎯 **Performance Assessment**
✅ **GOOD PERFORMANCE** - Suitable for production use.

## 📊 **Advanced Metrics**
- **RAE**: 0.4504
- **RRSE**: 0.5106
- **NRMSE**: 0.1295
- **Prediction Ratio**: 1.0250

Generated on: 20250805_102305
