import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import StandardScaler, RobustScaler, LabelEncoder, MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Tu<PERSON>, Dict, List
import warnings
import json
from datetime import datetime
warnings.filterwarnings('ignore')

class AdvancedQueryPredictor(nn.Module):
    """Advanced neural network for query time prediction using MSE loss"""

    def __init__(self, input_size: int, hidden_sizes: List[int] = None):
        # Note: Dropout layers are commented out in the architecture
        super(AdvancedQueryPredictor, self).__init__()

        self.input_size = input_size
        # FIXED: Avoid mutable default argument
        self.hidden_sizes = hidden_sizes if hidden_sizes is not None else [64, 32,16 ]

        # Input layer - CHANGED: Tanh activation as requested
        self.input_layer = nn.Sequential(
            nn.Linear(input_size, hidden_sizes[0]),
            # BatchNorm1d removed: Causes instability with small batch sizes
            nn.Tanh(),  # CHANGED: Tanh activation as requested
            nn.Dropout(0.1)  # ENABLED: Conservative dropout for regularization
        )

        # Hidden layers - CHANGED: Tanh activation as requested
        self.hidden_layers = nn.ModuleList()
        for i in range(len(hidden_sizes) - 1):
            layer = nn.Sequential(
                nn.Linear(hidden_sizes[i], hidden_sizes[i + 1]),
                # BatchNorm1d removed: Causes instability with small batch sizes
                nn.Tanh(),  # CHANGED: Tanh activation as requested
                nn.Dropout(0.1)  # ENABLED: Conservative dropout for regularization
            )
            self.hidden_layers.append(layer)

        # Single output layer for regression
        self.output_layer = nn.Linear(hidden_sizes[-1], 1)

        # Initialize weights properly
        self._initialize_weights()

    def _initialize_weights(self):
        """CHANGED: Correct layer-wise initialization for Tanh activation functions"""

        # Initialize input layer (followed by Tanh) - CHANGED: Xavier initialization for Tanh
        if hasattr(self.input_layer, '0') and isinstance(self.input_layer[0], nn.Linear):
            # Xavier/Glorot initialization for Tanh activation
            nn.init.xavier_normal_(self.input_layer[0].weight, gain=nn.init.calculate_gain('tanh'))
            nn.init.zeros_(self.input_layer[0].bias)

        # Initialize hidden layers (each followed by Tanh) - CHANGED: Xavier initialization for Tanh
        for layer in self.hidden_layers:
            if hasattr(layer, '0') and isinstance(layer[0], nn.Linear):
                # Xavier/Glorot initialization for Tanh activation
                nn.init.xavier_normal_(layer[0].weight, gain=nn.init.calculate_gain('tanh'))
                nn.init.zeros_(layer[0].bias)

        # Initialize output layer (no activation, so use Xavier with linear gain)
        if isinstance(self.output_layer, nn.Linear):
            # Xavier initialization for linear output (no activation)
            nn.init.xavier_normal_(self.output_layer.weight, gain=nn.init.calculate_gain('linear'))
            nn.init.zeros_(self.output_layer.bias)

    # REMOVED: _initialize_weights_he_relu method - INCORRECT for Tanh activations
    # The model uses Tanh activation, not ReLU, so He initialization is inappropriate

    # REMOVED: All He initialization methods - INCORRECT for Tanh activations
    # The model uses Tanh activation throughout, so only Xavier initialization is appropriate

    def forward(self, x):
        # Input layer
        x = self.input_layer(x)

        # Hidden layers - FIXED: Removed broken residual connections
        # Residual connections don't work with changing dimensions (128->64->32)
        for layer in self.hidden_layers:
            x = layer(x)

        return self.output_layer(x)


class AdvancedFeatureExtractor:
    """Enhanced feature extraction and engineering for query execution plans"""

    def __init__(self):
        self.scalers = {
            'numerical': RobustScaler(),
            'categorical': LabelEncoder(),
            'temporal': MinMaxScaler()
        }
        self.feature_selector = None
        self.selected_features = None

    def extract_advanced_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Extract advanced features from execution plan data"""

        # Fix: Strip whitespace from column names to handle inconsistencies
        df.columns = df.columns.str.strip()

        # SPECIFIC FEATURES: Use exactly the 10 specified features from train2.csv
        specific_features = [
            'EstimatedTotalSubtreeCostHashMatch',
            'EstimateRowsHashMatch',
            'total_num_joins',
            'ClusteredIndexScanOpCount',
            'ClusteredIndexSeekOpCount',
            'SortOpCount',
            'total_estimated_cpu_cost',  # Fixed: No trailing space
            'total_estimated_io_cost',
            'EstimateRowsSort',
            'HashMatchOpCount'
        ]
        
        print(f"Using SPECIFIC 10 features: {specific_features}")
        
        # Check which features are available in the dataset
        available_features = [f for f in specific_features if f in df.columns]
        missing_features = [f for f in specific_features if f not in df.columns]
        
        if missing_features:
            print(f"Warning: Missing features: {missing_features}")
        
        print(f"Available features ({len(available_features)}): {available_features}")
        
        # Extract only available specific features
        if available_features:
            feature_df = df[available_features].copy()
        else:
            raise ValueError("None of the specified features are available in the dataset!")
        
        # Handle missing values
        feature_df = feature_df.fillna(0)
        
        print(f"Final feature matrix shape: {feature_df.shape}")
        return feature_df

    def fit_transform(self, df: pd.DataFrame, target_col: str = 'QueryTime') -> Tuple[np.ndarray, np.ndarray]:
        """Fit transformers and transform the data"""
        
        # Extract features
        feature_df = self.extract_advanced_features(df)
        
        # Get target
        y = df[target_col].values
        
        # Scale features
        X_scaled = self.scalers['numerical'].fit_transform(feature_df)
        
        self.selected_features = feature_df.columns.tolist()
        
        return X_scaled, y

    def transform(self, df: pd.DataFrame) -> np.ndarray:
        """Transform new data using fitted transformers"""
        
        # Extract same features
        feature_df = self.extract_advanced_features(df)
        
        # Ensure same features as training
        if self.selected_features:
            # Reorder columns to match training
            feature_df = feature_df.reindex(columns=self.selected_features, fill_value=0)
        
        # Scale features
        X_scaled = self.scalers['numerical'].transform(feature_df)
        
        return X_scaled


def calculate_comprehensive_metrics(y_true, y_pred):
    """Calculate comprehensive evaluation metrics with numerical stability"""

    # ADDED: Numerical stability checks
    y_true = np.asarray(y_true)
    y_pred = np.asarray(y_pred)

    # Clip extreme values to prevent numerical instability
    max_val = 1e8  # Maximum reasonable value for query times (100 million ms = ~27 hours)
    y_pred = np.clip(y_pred, 0, max_val)

    # Basic regression metrics with numerical stability
    mse = mean_squared_error(y_true, y_pred)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(y_true, y_pred)

    # R² with numerical stability check
    try:
        r2 = r2_score(y_true, y_pred)
        # Clip R² to reasonable range (can be negative but not extremely so)
        r2 = np.clip(r2, -1000, 1.0)
    except:
        r2 = -999.0  # Indicate calculation failed
    
    # Additional metrics - FIXED: Proper MAPE calculation with filtering
    epsilon = 1e-8  # Threshold for filtering near-zero values
    mask = np.abs(y_true) > epsilon  # Filter out near-zero actual values
    if np.any(mask):
        mape = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100
    else:
        mape = 0.0  # If all values are near zero, MAPE is undefined
    
    # Relative metrics
    rae = np.sum(np.abs(y_true - y_pred)) / np.sum(np.abs(y_true - np.mean(y_true)))
    rrse = np.sqrt(np.sum((y_true - y_pred) ** 2) / np.sum((y_true - np.mean(y_true)) ** 2))
    
    # Prediction ratio
    pr = np.sum(y_pred) / np.sum(y_true) if np.sum(y_true) != 0 else 0
    
    # Normalized RMSE
    nrmse = rmse / (np.max(y_true) - np.min(y_true)) if np.max(y_true) != np.min(y_true) else 0
    
    # Correlation metrics - Fixed with proper error handling
    try:
        pearson_corr = np.corrcoef(y_true, y_pred)[0, 1]
        if np.isnan(pearson_corr):
            pearson_corr = 0.0
    except:
        pearson_corr = 0.0

    try:
        spearman_result = stats.spearmanr(y_true, y_pred)
        spearman_corr = spearman_result.correlation if hasattr(spearman_result, 'correlation') else spearman_result[0]
        if np.isnan(spearman_corr):
            spearman_corr = 0.0
    except:
        spearman_corr = 0.0
    
    metrics = {
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'mape': mape,
        'rae': rae,
        'rrse': rrse,
        'pr': pr,
        'nrmse': nrmse,
        'pearson_corr': pearson_corr,
        'spearman_corr': spearman_corr
    }
    
    return metrics

def print_metrics(metrics: Dict, title: str = "Model Performance"):
    """Print metrics in a formatted way"""
    print(f"\n{title}")
    print("=" * len(title))
    
    # Basic metrics
    print(f"Basic Regression Metrics:")
    print(f"  RMSE: {metrics['rmse']:,.2f}")
    print(f"  MAE: {metrics['mae']:,.2f}")
    print(f"  R²: {metrics['r2']:.4f}")
    print(f"  MAPE: {metrics['mape']:.2f}%")
    
    # Advanced metrics
    print(f"\nAdvanced Evaluation Metrics:")
    print(f"  RAE: {metrics['rae']:.4f}")
    print(f"  RRSE: {metrics['rrse']:.4f}")
    print(f"  PR: {metrics['pr']:,.2f}")
    print(f"  NRMSE: {metrics['nrmse']:.4f}")
    
    # Correlation metrics
    print(f"\nCorrelation Metrics:")
    print(f"  Pearson: {metrics['pearson_corr']:.4f}")
    print(f"  Spearman: {metrics['spearman_corr']:.4f}")


def evaluate_model_performance(model, X_test, y_test_orig, preprocessor, device):
    """Evaluate model performance with comprehensive metrics"""
    
    model.eval()
    with torch.no_grad():
        X_test_tensor = torch.FloatTensor(X_test).to(device)
        
        # Standard model prediction
        test_pred_scaled = model(X_test_tensor).cpu().numpy()
    
    # Inverse transform predictions - RESTORED: StandardScaler + expm1
    test_pred_log = preprocessor.target_scaler.inverse_transform(test_pred_scaled)
    test_pred_orig = np.expm1(test_pred_log.flatten())
    test_pred_orig = np.maximum(test_pred_orig, 0)
    
    # Calculate comprehensive metrics
    metrics = calculate_comprehensive_metrics(y_test_orig, test_pred_orig)
    
    return metrics, test_pred_orig


class DataPreprocessor:
    """Handle data preprocessing with proper scaling"""
    
    def __init__(self):
        self.feature_scaler = RobustScaler()
        self.target_scaler = StandardScaler()
        
    def fit_transform_features(self, X):
        return self.feature_scaler.fit_transform(X)
    
    def transform_features(self, X):
        return self.feature_scaler.transform(X)
    
    def fit_transform_target(self, y):
        # HYBRID APPROACH: Log transformation + StandardScaler for better training stability
        # Log transformation handles skewed distribution, StandardScaler normalizes for neural network
        y_log = np.log1p(y.values if hasattr(y, 'values') else y)
        return self.target_scaler.fit_transform(y_log.reshape(-1, 1)).flatten()

    def transform_target(self, y):
        # HYBRID APPROACH: Log transformation + StandardScaler for better training stability
        y_log = np.log1p(y.values if hasattr(y, 'values') else y)
        return self.target_scaler.transform(y_log.reshape(-1, 1)).flatten()


def generate_test_predictions(model, X_test, preprocessor, device):
    """Generate predictions for test set"""
    
    model.eval()
    with torch.no_grad():
        X_test_tensor = torch.FloatTensor(X_test).to(device)
        test_pred_scaled = model(X_test_tensor).cpu().numpy()
    
    # Inverse transform predictions - RESTORED: StandardScaler + expm1
    test_pred_log = preprocessor.target_scaler.inverse_transform(test_pred_scaled)
    test_pred_orig = np.expm1(test_pred_log.flatten())
    test_pred_orig = np.maximum(test_pred_orig, 0)
    
    return {'mean': test_pred_orig}


def create_comprehensive_visualizations(train_losses, test_losses, train_r2_scores, test_r2_scores, train_rmse_scores, test_rmse_scores,
                                       y_test_orig, test_pred_orig, final_metrics, timestamp):
    """Create comprehensive 12-panel visualization with CORRECT train vs test comparisons"""

    print("Creating comprehensive visualizations...")

    # Set up the plotting style
    plt.style.use('default')
    sns.set_palette("husl")

    # Create figure with 12 subplots (4 rows, 3 columns)
    fig = plt.figure(figsize=(20, 24))

    # 1. CORRECTED: Training Loss vs Test Loss
    ax1 = plt.subplot(4, 3, 1)
    epochs = range(len(train_losses))

    # Plot both training and test losses on same scale
    plt.plot(epochs, train_losses, 'b-', label='Training Loss', alpha=0.8, linewidth=2)
    plt.plot(epochs, test_losses, 'r-', label='Test Loss', alpha=0.8, linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('Loss (MSE)')
    plt.title('Training Loss vs Test Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.yscale('log')  # Log scale for better loss visualization

    # 2. CORRECTED: Training vs Test R² Comparison
    plt.subplot(4, 3, 2)
    plt.plot(epochs, train_r2_scores, 'b-', label='TRAINING R²', alpha=0.8, linewidth=2)
    plt.plot(epochs, test_r2_scores, 'r-', label='TEST R²', alpha=0.8, linewidth=2)
    plt.axhline(y=0.7, color='orange', linestyle='--', alpha=0.7, label='Good (0.7)')
    plt.axhline(y=0.9, color='gray', linestyle='--', alpha=0.7, label='Excellent (0.9)')
    plt.xlabel('Epoch')
    plt.ylabel('R² Score')
    plt.title('TRAINING vs TEST R² Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 3. CORRECTED: Training vs Test RMSE Comparison
    plt.subplot(4, 3, 3)
    plt.plot(epochs, train_rmse_scores, 'b-', label='TRAINING RMSE', alpha=0.8, linewidth=2)
    plt.plot(epochs, test_rmse_scores, 'r-', label='TEST RMSE', alpha=0.8, linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('RMSE (ms)')
    plt.title('TRAINING vs TEST RMSE Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 4. Predictions vs Actual (Log Scale)
    ax4 = plt.subplot(4, 3, 4)
    plt.scatter(y_test_orig, test_pred_orig, alpha=0.6, s=30)

    # Perfect prediction line
    min_val = min(y_test_orig.min(), test_pred_orig.min())
    max_val = max(y_test_orig.max(), test_pred_orig.max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='Perfect Prediction')

    plt.xlabel('Actual Query Time (ms)')
    plt.ylabel('Predicted Query Time (ms)')
    plt.title(f'Predictions vs Actual\\nR² = {final_metrics["r2"]:.4f}')
    plt.xscale('log')
    plt.yscale('log')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 5. Residuals Plot
    ax5 = plt.subplot(4, 3, 5)
    residuals = y_test_orig - test_pred_orig
    plt.scatter(test_pred_orig, residuals, alpha=0.6, s=30)
    plt.axhline(y=0, color='r', linestyle='--', alpha=0.8)
    plt.xlabel('Predicted Query Time (ms)')
    plt.ylabel('Residuals (ms)')
    plt.title('Residuals vs Predicted')
    plt.xscale('log')
    plt.grid(True, alpha=0.3)

    # 6. Error Distribution
    ax6 = plt.subplot(4, 3, 6)
    plt.hist(residuals, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.axvline(residuals.mean(), color='red', linestyle='--',
                label=f'Mean: {residuals.mean():.1f} ms')
    plt.xlabel('Residuals (ms)')
    plt.ylabel('Frequency')
    plt.title('Error Distribution')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 7. Performance Metrics Summary
    ax7 = plt.subplot(4, 3, 7)
    metrics = ['R²', 'RMSE\\n(×1000)', 'MAE\\n(×1000)', 'MAPE\\n(%/10)', 'RAE\\n(×10)']
    values = [final_metrics['r2'], final_metrics['rmse']/1000, final_metrics['mae']/1000,
              final_metrics['mape']/10, final_metrics['rae']*10]
    colors = ['green', 'blue', 'orange', 'red', 'purple']

    bars = plt.bar(metrics, values, color=colors, alpha=0.7)
    plt.title('Performance Metrics Summary')
    plt.ylabel('Normalized Values')

    # Add value labels on bars
    for bar, value in zip(bars, values):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{value:.3f}', ha='center', va='bottom')

    plt.grid(True, alpha=0.3)

    # 8. CORRECTED: All Loss Curves Comparison
    plt.subplot(4, 3, 8)

    # Normalize losses for comparison (0-1 scale)
    train_loss_norm = np.array(train_losses)
    train_loss_norm = (train_loss_norm - train_loss_norm.min()) / (train_loss_norm.max() - train_loss_norm.min())

    test_loss_norm = np.array(test_losses)
    test_loss_norm = (test_loss_norm - test_loss_norm.min()) / (test_loss_norm.max() - test_loss_norm.min())

    test_r2_norm = np.array(test_r2_scores)
    test_r2_norm = (test_r2_norm - test_r2_norm.min()) / (test_r2_norm.max() - test_r2_norm.min())

    plt.plot(epochs, train_loss_norm, 'b-', label='Training Loss (norm)', alpha=0.8, linewidth=2)
    plt.plot(epochs, test_loss_norm, 'r-', label='Test Loss (norm)', alpha=0.8, linewidth=2)
    plt.plot(epochs, test_r2_norm, 'g-', label='Test R² (norm)', alpha=0.8, linewidth=2)
    plt.xlabel('Epoch')
    plt.ylabel('Normalized Metrics (0-1)')
    plt.title('CORRECTED: All Metrics Normalized Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 9. Error by Query Size
    ax9 = plt.subplot(4, 3, 9)

    # Define size categories
    size_ranges = [(0, 1000), (1000, 5000), (5000, 20000), (20000, 50000), (50000, float('inf'))]
    size_labels = ['Very Small\\n(<1K)', 'Small\\n(1-5K)', 'Medium\\n(5-20K)', 'Large\\n(20-50K)', 'Very Large\\n(>50K)']

    category_errors = []
    category_counts = []

    for min_val, max_val in size_ranges:
        mask = (y_test_orig >= min_val) & (y_test_orig < max_val)
        if mask.sum() > 0:
            cat_mae = np.mean(np.abs(residuals[mask]))
            category_errors.append(cat_mae)
            category_counts.append(mask.sum())
        else:
            category_errors.append(0)
            category_counts.append(0)

    bars = plt.bar(size_labels, category_errors, alpha=0.7, color='lightcoral')
    plt.ylabel('Mean Absolute Error (ms)')
    plt.title('Error by Query Size Category')
    plt.xticks(rotation=45)

    # Add count labels
    for bar, count in zip(bars, category_counts):
        if count > 0:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 50,
                    f'n={count}', ha='center', va='bottom', fontsize=8)

    plt.grid(True, alpha=0.3)

    # 10. Training Stability
    ax10 = plt.subplot(4, 3, 10)

    # Calculate training volatility
    if len(train_losses) > 10:
        train_volatility = np.std(np.diff(train_losses[-50:]))  # Last 50 epochs
    else:
        train_volatility = 0

    volatility_data = [train_volatility]
    volatility_labels = ['Training']
    colors = ['blue']

    bars = plt.bar(volatility_labels, volatility_data, color=colors, alpha=0.7)
    plt.ylabel('Loss Volatility (Std of Differences)')
    plt.title('Training Stability\\n(Lower is Better)')

    for bar, value in zip(bars, volatility_data):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value:.4f}', ha='center', va='bottom')

    plt.grid(True, alpha=0.3)

    # 11. Advanced Metrics Visualization
    ax11 = plt.subplot(4, 3, 11)

    advanced_metrics = ['RAE', 'RRSE', 'NRMSE', 'Pearson', 'Spearman']
    advanced_values = [final_metrics['rae'], final_metrics['rrse'], final_metrics['nrmse'],
                      final_metrics['pearson_corr'], final_metrics['spearman_corr']]

    # Normalize values for better visualization
    normalized_values = []
    for i, (metric, value) in enumerate(zip(advanced_metrics, advanced_values)):
        if metric in ['RAE', 'RRSE', 'NRMSE']:
            # For error metrics, lower is better, so invert
            normalized_values.append(max(0, 1 - value))
        else:
            # For correlation metrics, higher is better
            normalized_values.append(max(0, value))

    colors = ['orange', 'red', 'purple', 'green', 'blue']
    bars = plt.bar(advanced_metrics, normalized_values, color=colors, alpha=0.7)
    plt.ylabel('Normalized Score (Higher is Better)')
    plt.title('Advanced Metrics Comparison')
    plt.ylim(0, 1)

    for bar, orig_value in zip(bars, advanced_values):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{orig_value:.3f}', ha='center', va='bottom', fontsize=8)

    plt.grid(True, alpha=0.3)

    # 12. Model Quality Assessment
    ax12 = plt.subplot(4, 3, 12)

    # Quality indicators
    quality_metrics = {
        'R² Score': final_metrics['r2'],
        'Low MAPE': max(0, 1 - final_metrics['mape']/100),
        'High Correlation': final_metrics['pearson_corr'],
        'Low RAE': max(0, 1 - final_metrics['rae']),
        'Low RRSE': max(0, 1 - final_metrics['rrse'])
    }

    # Create radar-like visualization
    metrics_names = list(quality_metrics.keys())
    metrics_values = list(quality_metrics.values())

    # Color code based on quality
    colors = ['green' if v > 0.7 else 'orange' if v > 0.5 else 'red' for v in metrics_values]

    bars = plt.barh(metrics_names, metrics_values, color=colors, alpha=0.7)
    plt.xlabel('Quality Score (0-1)')
    plt.title('Model Quality Assessment')
    plt.xlim(0, 1)

    # Add value labels
    for bar, value in zip(bars, metrics_values):
        width = bar.get_width()
        plt.text(width + 0.02, bar.get_y() + bar.get_height()/2.,
                f'{value:.3f}', ha='left', va='center')

    # Add quality threshold lines
    plt.axvline(x=0.7, color='green', linestyle='--', alpha=0.5, label='Excellent (0.7+)')
    plt.axvline(x=0.5, color='orange', linestyle='--', alpha=0.5, label='Good (0.5+)')
    plt.legend(fontsize=8)
    plt.grid(True, alpha=0.3)

    # Adjust layout and save
    plt.tight_layout()

    # Save the comprehensive visualization
    filename = f'output/train_test_only_results_{timestamp}.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()

    print(f"Comprehensive visualization saved as: {filename}")

    return filename


def create_markdown_summary(final_metrics, results, timestamp, epochs, batch_size, learning_rate):
    """Create a comprehensive markdown summary and save it in the summary folder"""

    summary_content = f"""# Train-Test Only Model Results Summary

## 🎯 **Training Configuration**
- **Epochs**: {epochs}
- **Batch Size**: {batch_size}
- **Learning Rate**: {learning_rate}
- **Training Approach**: Train-Test Only (no validation set)
- **Features**: 10 specific features from train2.csv and test2.csv
- **Timestamp**: {timestamp}

## 📊 **Final Performance Results**

| Metric | Value | Status |
|--------|-------|--------|
| **R² Score** | **{final_metrics['r2']:.4f}** | {'✅ Excellent' if final_metrics['r2'] > 0.8 else '✅ Good' if final_metrics['r2'] > 0.7 else '⚠️ Moderate'} |
| **RMSE** | **{final_metrics['rmse']:,.0f} ms** | {'✅ Excellent' if final_metrics['rmse'] < 3000 else '✅ Good' if final_metrics['rmse'] < 5000 else '⚠️ Moderate'} |
| **MAE** | **{final_metrics['mae']:,.0f} ms** | {'✅ Excellent' if final_metrics['mae'] < 2000 else '✅ Good' if final_metrics['mae'] < 3000 else '⚠️ Moderate'} |
| **MAPE** | **{final_metrics['mape']:.2f}%** | {'✅ Excellent' if final_metrics['mape'] < 25 else '✅ Good' if final_metrics['mape'] < 50 else '⚠️ Moderate'} |
| **Pearson Correlation** | **{final_metrics['pearson_corr']:.4f}** | {'✅ Excellent' if final_metrics['pearson_corr'] > 0.9 else '✅ Good' if final_metrics['pearson_corr'] > 0.8 else '⚠️ Moderate'} |
| **Spearman Correlation** | **{final_metrics['spearman_corr']:.4f}** | {'✅ Excellent' if final_metrics['spearman_corr'] > 0.9 else '✅ Good' if final_metrics['spearman_corr'] > 0.8 else '⚠️ Moderate'} |

## 🎯 **Features Used (10 Specific Features)**
1. EstimatedTotalSubtreeCostHashMatch
2. EstimateRowsHashMatch
3. total_num_joins
4. ClusteredIndexScanOpCount
5. ClusteredIndexSeekOpCount
6. SortOpCount
7. total_estimated_cpu_cost
8. total_estimated_io_cost
9. EstimateRowsSort
10. HashMatchOpCount

## 🚀 **Model Architecture**
- **Input Features**: {results['model_config']['num_features']}
- **Hidden Layers**: {results['model_config']['architecture']}
- **Total Parameters**: {results['model_config']['total_parameters']:,}
- **Activation**: Tanh
- **Loss Function**: MSE Loss
- **Optimizer**: AdamW

## 📈 **Training Results**
- **Epochs Trained**: {results['training_history']['epochs_trained']}
- **Final Train Loss**: {results['training_history']['final_train_loss']:.6f}
- **Final Test R²**: {results['training_history']['final_test_r2']:.4f}
- **Final Test RMSE**: {results['training_history']['final_test_rmse']:.2f} ms

## 📁 **Output Files**
- **Model**: output/train_test_only_model_{timestamp}.pth
- **Results**: output/train_test_only_results_{timestamp}.json
- **Visualization**: output/train_test_only_results_{timestamp}.png
- **Best Model**: output/best_train_test_only_model.pth

## 🎯 **Performance Assessment**
{'🏆 **OUTSTANDING PERFORMANCE** - Ready for production deployment!' if final_metrics['r2'] > 0.8 and final_metrics['mape'] < 30 else '✅ **GOOD PERFORMANCE** - Suitable for production use.' if final_metrics['r2'] > 0.7 and final_metrics['mape'] < 50 else '⚠️ **MODERATE PERFORMANCE** - Consider further optimization.'}

## 📊 **Advanced Metrics**
- **RAE**: {final_metrics['rae']:.4f}
- **RRSE**: {final_metrics['rrse']:.4f}
- **NRMSE**: {final_metrics['nrmse']:.4f}
- **Prediction Ratio**: {final_metrics['pr']:.4f}

Generated on: {timestamp}
"""

    # Save markdown summary in summary folder
    summary_file = f'summary/train_test_only_summary_{timestamp}.md'
    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(summary_content)

    print(f"Markdown summary saved as: {summary_file}")
    return summary_file


def train_model_train_test_only(X_train, X_test, y_train, y_test, y_train_orig, y_test_orig,
                               preprocessor, epochs=10000, batch_size=16, learning_rate=0.001):
    """Train the neural network model with full test monitoring (restored as requested)"""

    print("\\nStarting train-test model training with full monitoring...")
    print(f"Training samples: {len(X_train)}")
    print(f"Test samples: {len(X_test)}")
    print(f"Features: {X_train.shape[1]}")

    # Device selection with GPU memory error handling
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    if device.type == 'cuda':
        try:
            # Test GPU memory availability
            torch.cuda.empty_cache()
            print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        except Exception as e:
            print(f"GPU memory warning: {e}")
            device = torch.device('cpu')
            print("Falling back to CPU")

    # Create model - FIXED: Clean architecture without unused parameters
    model = AdvancedQueryPredictor(
        input_size=X_train.shape[1],
        hidden_sizes=[128, 64, 32]
    ).to(device)

    print(f"Model parameters: {sum(p.numel() for p in model.parameters()):,}")

    # Loss and optimizer - NOTE: MSE loss on log-scaled targets, metrics on original scale
    # This is intentional: training optimizes log-scale MSE, evaluation uses original scale
    criterion = nn.MSELoss()
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.001)  # FIXED: Less aggressive weight decay
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        patience=100,     # CHANGED: Increased patience for 1000 epochs
        factor=0.7,       # CHANGED: Less aggressive reduction for longer training
        min_lr=1e-6       # CHANGED: Higher minimum LR for Tanh networks
        # Note: verbose parameter doesn't exist in ReduceLROnPlateau
    )

    # Data loaders - FIXED: Adaptive batch size for small test set
    train_dataset = TensorDataset(torch.FloatTensor(X_train), torch.FloatTensor(y_train))
    test_dataset = TensorDataset(torch.FloatTensor(X_test), torch.FloatTensor(y_test))

    # FIXED: Use smaller batch size for test set if dataset is small
    test_batch_size = min(batch_size, len(X_test) // 2) if len(X_test) < 50 else batch_size
    test_batch_size = max(test_batch_size, 1)  # Ensure at least 1

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=test_batch_size, shuffle=False)

    print(f"Training batch size: {batch_size}, Test batch size: {test_batch_size}")

    # Training history - FIXED: Track all metrics for train and test
    train_losses = []
    test_losses = []
    train_r2_scores = []  # ADDED: Track training R²
    test_r2_scores = []
    train_rmse_scores = []  # ADDED: Track training RMSE
    test_rmse_scores = []

    best_test_loss = float('inf')  # FIXED: Track test loss for proper early stopping
    patience_counter = 0
    patience = 200  # CHANGED: Increased patience for 1000 epochs training
    min_delta = 1e-7  # CHANGED: Smaller threshold for longer training

    print(f"\\nTraining for up to {epochs} epochs...")
    print("Epoch | TrLoss   | TrR²   | TeR²   | TrRMSE  | TeRMSE  | TrMAPE | TeMAPE | LR")
    print("      | (scaled) | (scld) | (scld) | (rob95) | (rob95) | (%)    | (%)    |")
    print("-" * 95)

    for epoch in range(epochs):
        # Training phase
        model.train()
        train_loss = 0.0

        for batch_X, batch_y in train_loader:
            batch_X, batch_y = batch_X.to(device), batch_y.to(device)

            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs.squeeze(), batch_y)
            loss.backward()

            # Gradient clipping - CHANGED: Less aggressive for Tanh (better gradient flow)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=5.0)

            optimizer.step()
            train_loss += loss.item()

        train_loss /= len(train_loader)

        # RESTORED: Test evaluation during training for monitoring (as requested)
        model.eval()
        test_loss = 0.0
        test_predictions = []
        test_targets = []

        with torch.no_grad():
            for batch_X, batch_y in test_loader:
                batch_X, batch_y = batch_X.to(device), batch_y.to(device)
                outputs = model(batch_X)
                loss = criterion(outputs.squeeze(), batch_y)
                test_loss += loss.item()

                # FIXED: More efficient memory handling
                test_predictions.append(outputs.squeeze().detach().cpu().numpy())
                test_targets.append(batch_y.detach().cpu().numpy())

        # FIXED: Concatenate arrays more efficiently
        test_predictions = np.concatenate(test_predictions)
        test_targets = np.concatenate(test_targets)

        test_loss /= len(test_loader)

        # CRITICAL FIX: Calculate test metrics on SCALED data (same scale as model training)
        # This ensures consistency with training metrics and loss calculation
        try:
            test_r2 = r2_score(test_targets, test_predictions)
            test_r2 = np.clip(test_r2, -1000, 1.0)
        except:
            test_r2 = -999.0

        # Calculate BOTH scaled RMSE (for consistency with loss) AND original RMSE (for interpretability)
        test_rmse_scaled = np.sqrt(mean_squared_error(test_targets, test_predictions))

        # Convert to original scale for RMSE and MAPE calculation
        test_pred_log = preprocessor.target_scaler.inverse_transform(test_predictions.reshape(-1, 1))
        test_pred_orig = np.expm1(test_pred_log.flatten())
        test_pred_orig = np.maximum(test_pred_orig, 0)

        test_targets_log = preprocessor.target_scaler.inverse_transform(test_targets.reshape(-1, 1))
        test_targets_orig = np.expm1(test_targets_log.flatten())
        test_targets_orig = np.maximum(test_targets_orig, 0)

        test_pred_orig = np.clip(test_pred_orig, 0, 1e8)

        # Calculate ROBUST RMSE in milliseconds (exclude extreme outliers for stable monitoring)
        # Use 95th percentile to exclude extreme outliers that dominate RMSE
        mask = test_targets_orig < np.percentile(test_targets_orig, 95)
        if np.sum(mask) > 5:  # Ensure we have enough samples (test set is small)
            test_rmse = np.sqrt(mean_squared_error(test_targets_orig[mask], test_pred_orig[mask]))
        else:
            test_rmse = np.sqrt(mean_squared_error(test_targets_orig, test_pred_orig))

        # Calculate MAPE for test
        epsilon = 1e-8
        mask = np.abs(test_targets_orig) > epsilon
        if np.any(mask):
            test_mape = np.mean(np.abs((test_targets_orig[mask] - test_pred_orig[mask]) / test_targets_orig[mask])) * 100
        else:
            test_mape = 0.0

        # Calculate training R² and RMSE for comparison - CRITICAL FIX: Use eval mode for consistent metrics
        model.eval()  # FIXED: Use eval mode to disable dropout for consistent metric calculation
        train_predictions = []
        train_targets = []
        with torch.no_grad():
            for batch_X, batch_y in train_loader:
                batch_X, batch_y = batch_X.to(device), batch_y.to(device)
                outputs = model(batch_X)  # Clean predictions without dropout noise
                # CRITICAL FIX: Collect both predictions AND corresponding targets
                train_predictions.append(outputs.squeeze().detach().cpu().numpy())
                train_targets.append(batch_y.detach().cpu().numpy())

        # FIXED: Concatenate arrays more efficiently
        train_predictions = np.concatenate(train_predictions)
        train_targets = np.concatenate(train_targets)

        # CRITICAL FIX: Calculate training metrics on SCALED data (same scale as model training)
        # This ensures consistency with loss calculation and proper monitoring
        try:
            train_r2 = r2_score(train_targets, train_predictions)
            train_r2 = np.clip(train_r2, -1000, 1.0)
        except:
            train_r2 = -999.0

        # Calculate BOTH scaled RMSE (for consistency with loss) AND original RMSE (for interpretability)
        train_rmse_scaled = np.sqrt(mean_squared_error(train_targets, train_predictions))

        # Convert to original scale for RMSE and MAPE calculation
        train_pred_log = preprocessor.target_scaler.inverse_transform(train_predictions.reshape(-1, 1))
        train_pred_orig = np.expm1(train_pred_log.flatten())
        train_pred_orig = np.maximum(train_pred_orig, 0)

        train_targets_log = preprocessor.target_scaler.inverse_transform(train_targets.reshape(-1, 1))
        train_targets_orig = np.expm1(train_targets_log.flatten())
        train_targets_orig = np.maximum(train_targets_orig, 0)

        train_pred_orig = np.clip(train_pred_orig, 0, 1e8)

        # Calculate ROBUST RMSE in milliseconds (exclude extreme outliers for stable monitoring)
        # Use 95th percentile to exclude extreme outliers that dominate RMSE
        mask = train_targets_orig < np.percentile(train_targets_orig, 95)
        if np.sum(mask) > 10:  # Ensure we have enough samples
            train_rmse = np.sqrt(mean_squared_error(train_targets_orig[mask], train_pred_orig[mask]))
        else:
            train_rmse = np.sqrt(mean_squared_error(train_targets_orig, train_pred_orig))

        # Calculate MAPE for training (on original scale)
        epsilon = 1e-8
        mask = np.abs(train_targets_orig) > epsilon
        if np.any(mask):
            train_mape = np.mean(np.abs((train_targets_orig[mask] - train_pred_orig[mask]) / train_targets_orig[mask])) * 100
        else:
            train_mape = 0.0

        # Update learning rate - FIXED: Use test loss for proper LR scheduling
        scheduler.step(test_loss)

        # Track metrics - FIXED: Track all train and test metrics
        train_losses.append(train_loss)
        test_losses.append(test_loss)
        train_r2_scores.append(train_r2)  # ADDED: Track training R²
        test_r2_scores.append(test_r2)
        train_rmse_scores.append(train_rmse)  # ADDED: Track training RMSE
        test_rmse_scores.append(test_rmse)

        # Early stopping based on test loss - FIXED: Use test loss for proper early stopping
        if test_loss < best_test_loss - min_delta:
            best_test_loss = test_loss  # Track best test loss for early stopping
            patience_counter = 0
            torch.save(model.state_dict(), 'output/best_train_test_only_model.pth')
        else:
            patience_counter += 1

        # Print progress - FIXED: Show training and test metrics with MAPE
        if epoch % 50 == 0 or epoch < 10:
            current_lr = optimizer.param_groups[0]['lr']
            print(f"{epoch:5d} | TrLoss:{train_loss:8.6f} | TrR²:{train_r2:6.4f} | TeR²:{test_r2:6.4f} | TrRMSE:{train_rmse:7.1f} | TeRMSE:{test_rmse:7.1f} | TrMAPE:{train_mape:5.1f}% | TeMAPE:{test_mape:5.1f}% | LR:{current_lr:.2e}")

        # Early stopping
        if patience_counter >= patience:
            print(f"\\nEarly stopping at epoch {epoch} (patience: {patience})")
            break

    # Load best model
    model.load_state_dict(torch.load('output/best_train_test_only_model.pth'))

    # Final test evaluation with best model to get final predictions
    model.eval()
    final_test_predictions = []
    with torch.no_grad():
        for batch_X, batch_y in test_loader:
            batch_X = batch_X.to(device)
            outputs = model(batch_X)
            final_test_predictions.extend(outputs.squeeze().cpu().numpy())

    # Convert final predictions to original scale - RESTORED: StandardScaler + expm1
    final_test_pred_log = preprocessor.target_scaler.inverse_transform(np.array(final_test_predictions).reshape(-1, 1))
    final_test_pred_orig = np.expm1(final_test_pred_log.flatten())
    final_test_pred_orig = np.maximum(final_test_pred_orig, 0)

    print(f"\\nTraining completed!")
    print(f"Best test loss: {best_test_loss:.6f}")
    print(f"Total epochs: {len(train_losses)}")

    return model, train_losses, test_losses, train_r2_scores, test_r2_scores, train_rmse_scores, test_rmse_scores, best_test_loss, final_test_pred_orig


def main(epochs=10000, batch_size=16, learning_rate=0.001):
    """Main execution function for train-test only setup with configurable parameters"""

    # Create output directories if they don't exist
    import os
    os.makedirs("output", exist_ok=True)
    os.makedirs("summary", exist_ok=True)

    print("="*80)
    print("NEURAL NETWORK MODEL - TRAIN AND TEST SETS ONLY")
    print("="*80)
    print(f"Training Configuration:")
    print(f"  Epochs: {epochs}")
    print(f"  Batch Size: {batch_size}")
    print(f"  Learning Rate: {learning_rate}")
    print(f"Output folders:")
    print(f"  Models/Results: output/")
    print(f"  Summaries: summary/")
    print("="*80)
    print("Features used (exactly as requested from train2.csv):")
    specific_features = [
        '1. EstimatedTotalSubtreeCostHashMatch',
        '2. EstimateRowsHashMatch',
        '3. total_num_joins',
        '4. ClusteredIndexScanOpCount',
        '5. ClusteredIndexSeekOpCount',
        '6. SortOpCount',
        '7. total_estimated_cpu_cost',
        '8. total_estimated_io_cost',
        '9. EstimateRowsSort',
        '10. HashMatchOpCount'
    ]
    for feature in specific_features:
        print(f"  {feature}")
    print("="*80)

    # Load data with error handling
    print("\\nLoading data...")
    try:
        train_df = pd.read_csv("Dataset/Dataset/train/train2.csv")
        test_df = pd.read_csv("Dataset/Dataset/test/test2.csv")
    except FileNotFoundError as e:
        print(f"Error: Could not find data files. {e}")
        return None, None, None
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

    print(f"Training data shape: {train_df.shape}")
    print(f"Test data shape: {test_df.shape}")

    # Initialize feature extractor
    feature_extractor = AdvancedFeatureExtractor()

    # Extract features and target
    print("\\nExtracting features...")
    X_train, y_train_orig = feature_extractor.fit_transform(train_df)
    X_test = feature_extractor.transform(test_df)
    y_test_orig = test_df['QueryTime']

    print(f"Training feature matrix shape: {X_train.shape}")
    print(f"Test feature matrix shape: {X_test.shape}")
    print(f"Training samples: {len(X_train)}")
    print(f"Test samples: {len(X_test)}")

    # Initialize preprocessor
    preprocessor = DataPreprocessor()

    # Preprocess features
    X_train_scaled = preprocessor.fit_transform_features(X_train)
    X_test_scaled = preprocessor.transform_features(X_test)

    # Preprocess target - RESTORED: Both training and test targets for monitoring
    y_train_scaled = preprocessor.fit_transform_target(y_train_orig)
    y_test_scaled = preprocessor.transform_target(y_test_orig)

    print(f"\\nData preprocessing completed")
    print(f"Scaled training features shape: {X_train_scaled.shape}")
    print(f"Scaled test features shape: {X_test_scaled.shape}")
    print(f"Scaled target range: [{y_train_scaled.min():.3f}, {y_train_scaled.max():.3f}]")

    # Train model with configurable parameters - FIXED: Include all train and test metrics
    model, train_losses, test_losses, train_r2_scores, test_r2_scores, train_rmse_scores, test_rmse_scores, best_train_loss, test_pred_orig = train_model_train_test_only(
        X_train_scaled, X_test_scaled, y_train_scaled, y_test_scaled,
        y_train_orig, y_test_orig, preprocessor,
        epochs=epochs, batch_size=batch_size, learning_rate=learning_rate
    )

    # Final evaluation using test predictions from training (no re-evaluation needed)
    final_metrics = calculate_comprehensive_metrics(y_test_orig, test_pred_orig)

    print_metrics(final_metrics, "Final Test Set Performance")

    # Create visualizations - FIXED: Include all train and test metrics
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    visualization_file = create_comprehensive_visualizations(
        train_losses, test_losses, train_r2_scores, test_r2_scores, train_rmse_scores, test_rmse_scores,
        y_test_orig, test_pred_orig, final_metrics, timestamp
    )

    # Save results
    results = {
        'model_config': {
            'features_used': feature_extractor.selected_features,
            'num_features': len(feature_extractor.selected_features),
            'architecture': [128, 64, 32],
            'total_parameters': sum(p.numel() for p in model.parameters()),
            'training_approach': 'train_test_only'
        },
        'training_history': {
            'epochs_trained': len(train_losses),
            'best_train_loss': best_train_loss,  # Fixed: Use actual best training loss
            'final_train_loss': train_losses[-1],
            # RESTORED: Test metrics during training available
            'final_test_r2': test_r2_scores[-1],  # From training monitoring
            'final_test_rmse': test_rmse_scores[-1]  # From training monitoring
        },
        'performance_metrics': final_metrics,
        'test_predictions': test_pred_orig.tolist()
    }

    # Save to JSON in output folder
    results_file = f'output/train_test_only_results_{timestamp}.json'
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)

    # Save model in output folder
    model_file = f'output/train_test_only_model_{timestamp}.pth'
    torch.save({
        'model_state_dict': model.state_dict(),
        'model_config': results['model_config'],
        'preprocessor_state': {
            'feature_scaler': preprocessor.feature_scaler,
            'target_scaler': preprocessor.target_scaler
        },
        'feature_names': feature_extractor.selected_features
    }, model_file)

    # Create markdown summary in summary folder
    summary_file = create_markdown_summary(final_metrics, results, timestamp, epochs, batch_size, learning_rate)

    print(f"\\n" + "="*80)
    print("TRAIN-TEST ONLY TRAINING COMPLETED SUCCESSFULLY!")
    print("="*80)
    print(f"Results saved to: {results_file}")
    print(f"Model saved to: {model_file}")
    print(f"Visualization saved to: {visualization_file}")
    print(f"Summary saved to: {summary_file}")
    print(f"\\nFinal Test Set Performance Summary:")
    print(f"  R² Score: {final_metrics['r2']:.4f}")
    print(f"  RMSE: {final_metrics['rmse']:,.2f} ms")
    print(f"  MAE: {final_metrics['mae']:,.2f} ms")
    print(f"  MAPE: {final_metrics['mape']:.2f}%")
    print(f"  Features used: {len(feature_extractor.selected_features)}")
    print(f"  Training approach: Train-Test only (no validation set)")
    print(f"\\nTraining Configuration Used:")
    print(f"  Epochs: {epochs}")
    print(f"  Batch Size: {batch_size}")
    print(f"  Learning Rate: {learning_rate}")
    print("="*80)

    return model, results, final_metrics


if __name__ == "__main__":
    # Configurable training parameters - CHANGED: Optimized for Tanh + 1000 epochs
    EPOCHS = 1000       # CHANGED: Increased to 1000 epochs as requested
    BATCH_SIZE = 32     # CHANGED: Increased batch size for longer training
    LEARNING_RATE = 0.0001 # CHANGED: Higher LR for Tanh (better than ReLU for gradients)

    # Run training with configurable parameters
    model, results, metrics = main(epochs=EPOCHS, batch_size=BATCH_SIZE, learning_rate=LEARNING_RATE)
