import pandas as pd
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import RobustScaler, StandardScaler
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import matplotlib.pyplot as plt
from typing import Tuple, Dict
import warnings
warnings.filterwarnings('ignore')

class ImprovedQueryPredictor(nn.Module):
    """Simplified neural network with better initialization"""
    
    def __init__(self, input_size: int, hidden_sizes: list = [128, 64]):
        super(ImprovedQueryPredictor, self).__init__()
        
        layers = []
        prev_size = input_size
        
        # Simpler architecture
        for hidden_size in hidden_sizes:
            layers.extend([
                nn.Linear(prev_size, hidden_size),
                nn.BatchNorm1d(hidden_size),
                nn.Tanh()
            ])
            prev_size = hidden_size
        
        # Output layer
        layers.append(nn.Linear(prev_size, 1))
        
        self.network = nn.Sequential(*layers)
        self._initialize_weights()
    
    def _initialize_weights(self):
        for module in self.modules():
            if isinstance(module, nn.Linear):
                # Better initialization for tanh
                nn.init.xavier_normal_(module.weight)
                nn.init.zeros_(module.bias)
    
    def forward(self, x):
        return self.network(x)

class ImprovedDataset:
    """Improved data preprocessing"""
    
    def __init__(self, train_path: str, test_path: str):
        self.train_path = train_path
        self.test_path = test_path
        self.feature_scaler = StandardScaler()
        self.target_scaler = StandardScaler()
        
    def load_and_preprocess(self):
        # Load data
        train_df = pd.read_csv(self.train_path)
        test_df = pd.read_csv(self.test_path)
        
        # Separate features and target
        feature_columns = [col for col in train_df.columns if col != 'QueryTime']
        X_train_full = train_df[feature_columns].values
        y_train_full = train_df['QueryTime'].values
        
        # Log transform target variable
        y_train_full = np.log1p(y_train_full).reshape(-1, 1)
        
        # Handle test data
        if 'QueryTime' in test_df.columns:
            X_test = test_df[feature_columns].values
            y_test = np.log1p(test_df['QueryTime'].values).reshape(-1, 1)
        else:
            X_test = test_df[feature_columns].values
            y_test = None
        
        # Handle missing values
        X_train_full = self._clean_data(X_train_full)
        X_test = self._clean_data(X_test)
        
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(
            X_train_full, y_train_full, test_size=0.2, random_state=42
        )
        
        # Scale features
        X_train_scaled = self.feature_scaler.fit_transform(X_train)
        y_train_scaled = self.target_scaler.fit_transform(y_train)
        
        X_val_scaled = self.feature_scaler.transform(X_val)
        y_val_scaled = self.target_scaler.transform(y_val)
        X_test_scaled = self.feature_scaler.transform(X_test)
        
        print(f"Data shapes - Train: {X_train_scaled.shape}, Val: {X_val_scaled.shape}, Test: {X_test_scaled.shape}")
        print(f"Target range - Min: {y_train_scaled.min():.3f}, Max: {y_train_scaled.max():.3f}")
        
        return (X_train_scaled, y_train_scaled, X_val_scaled, y_val_scaled,
                X_test_scaled, y_test, y_train, y_val)
    
    def _clean_data(self, X):
        # Replace inf with nan
        X = np.where(np.isinf(X), np.nan, X)
        
        # Fill missing values with median
        from sklearn.impute import SimpleImputer
        imputer = SimpleImputer(strategy='median')
        X = imputer.fit_transform(X)
        
        return X

def train_improved_model():
    """Train with better hyperparameters"""
    
    # Load data
    dataset = ImprovedDataset("Dataset/Dataset/train/train.csv", "Dataset/Dataset/test/test.csv")
    (X_train, y_train, X_val, y_val, X_test, y_test, y_train_orig, y_val_orig) = dataset.load_and_preprocess()
    
    # Create simpler model
    input_size = X_train.shape[1]
    model = ImprovedQueryPredictor(input_size, hidden_sizes=[128, 64])
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    print(f"Model: {input_size} -> 128 -> 64 -> 1")
    print(f"Device: {device}")
    print(f"Parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train).to(device)
    y_train_tensor = torch.FloatTensor(y_train).to(device)
    X_val_tensor = torch.FloatTensor(X_val).to(device)
    y_val_tensor = torch.FloatTensor(y_val).to(device)
    
    # Training setup
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    
    # Try different learning rates
    learning_rates = [0.01, 0.001, 0.0001]
    best_lr = None
    best_loss = float('inf')
    
    for lr in learning_rates:
        print(f"\nTesting learning rate: {lr}")
        
        # Reset model
        model = ImprovedQueryPredictor(input_size, hidden_sizes=[128, 64]).to(device)
        optimizer = optim.Adam(model.parameters(), lr=lr)
        criterion = nn.MSELoss()
        
        # Train for 10 epochs to test
        model.train()
        for epoch in range(10):
            total_loss = 0
            for batch_X, batch_y in train_loader:
                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                
                # Check gradients
                grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                optimizer.step()
                total_loss += loss.item()
            
            avg_loss = total_loss / len(train_loader)
            if epoch == 9:  # Last epoch
                print(f"Final loss: {avg_loss:.6f}, Grad norm: {grad_norm:.6f}")
                if avg_loss < best_loss:
                    best_loss = avg_loss
                    best_lr = lr
    
    print(f"\nBest learning rate: {best_lr} with loss: {best_loss:.6f}")
    
    # Train with best learning rate
    model = ImprovedQueryPredictor(input_size, hidden_sizes=[128, 64]).to(device)
    optimizer = optim.Adam(model.parameters(), lr=best_lr)
    criterion = nn.MSELoss()
    
    train_losses = []
    val_losses = []
    
    print("\nStarting full training...")
    for epoch in range(100):
        # Training
        model.train()
        train_loss = 0
        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            train_loss += loss.item()
        
        # Validation
        model.eval()
        with torch.no_grad():
            val_outputs = model(X_val_tensor)
            val_loss = criterion(val_outputs, y_val_tensor).item()
        
        avg_train_loss = train_loss / len(train_loader)
        train_losses.append(avg_train_loss)
        val_losses.append(val_loss)
        
        if epoch % 10 == 0:
            print(f'Epoch {epoch:3d}: Train Loss: {avg_train_loss:.6f}, Val Loss: {val_loss:.6f}')
    
    # Plot training curves
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, label='Training Loss')
    plt.plot(val_losses, label='Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training Progress')
    plt.legend()
    plt.grid(True)
    plt.savefig('improved_training_curves.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return model, dataset, train_losses, val_losses

if __name__ == "__main__":
    model, dataset, train_losses, val_losses = train_improved_model()